import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity cho Campaign (Chiến dịch)
 */
@Entity('campaign')
export class Campaign {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Tên chiến dịch
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * <PERSON>ô tả chiến dịch
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * ID người tạo chiến dịch
   */
  @Column({ type: 'int', nullable: false })
  createdBy: number;

  /**
   * ID người sở hữu chiến dịch
   */
  @Column({ type: 'int', nullable: false })
  ownerId: number;

  /**
   * <PERSON><PERSON><PERSON> bắt đầu chiến dịch (timestamp)
   */
  @Column({ type: 'bigint', nullable: true })
  startDate: number | null;

  /**
   * <PERSON><PERSON><PERSON> kế<PERSON> thúc chiến dịch (timestamp)
   */
  @Column({ type: 'bigint', nullable: true })
  endDate: number | null;

  /**
   * Trạng thái chiến dịch
   */
  @Column({ 
    type: 'enum', 
    enum: ['draft', 'active', 'paused', 'completed', 'cancelled'],
    default: 'draft'
  })
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';

  /**
   * Mức độ ưu tiên
   */
  @Column({ 
    type: 'enum', 
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  })
  priority: 'low' | 'medium' | 'high' | 'urgent';

  /**
   * Ngân sách chiến dịch
   */
  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  budget: number | null;

  /**
   * Đơn vị tiền tệ
   */
  @Column({ type: 'varchar', length: 10, nullable: true, default: 'VND' })
  currency: string | null;

  /**
   * Mục tiêu chiến dịch
   */
  @Column({ type: 'text', nullable: true })
  objectives: string | null;

  /**
   * Nhóm mục tiêu
   */
  @Column({ type: 'text', nullable: true })
  targetAudience: string | null;

  /**
   * Kênh truyền thông
   */
  @Column({ type: 'json', nullable: true })
  channels: string[] | null;

  /**
   * Tags/nhãn
   */
  @Column({ type: 'json', nullable: true })
  tags: string[] | null;

  /**
   * Metadata bổ sung
   */
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Có hoạt động hay không
   */
  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Thời gian tạo (timestamp)
   */
  @Column({ type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối (timestamp)
   */
  @Column({ type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID tenant (required for tenant isolation)
   */
  @Column({ type: 'int', nullable: false })
  tenantId: number;
}
