import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { TaskKrRepository } from '../repositories/task-kr.repository';
import { TodoRepository } from '../repositories/todo.repository';
import { CreateTaskKrDto } from '../dto/task-kr/create-task-kr.dto';
import { TaskKrResponseDto } from '../dto/task-kr/task-kr-response.dto';
import { TaskKrQueryDto } from '../dto/task-kr/task-kr-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Service xử lý logic nghiệp vụ cho task-kr mapping
 */
@Injectable()
export class TaskKrService {
  constructor(
    private readonly taskKrRepository: TaskKrRepository,
    private readonly todoRepository: TodoRepository,
  ) {}

  /**
   * T<PERSON><PERSON> liên kết giữa task và key results
   * @param tenantId ID tenant
   * @param createDto Dữ liệu tạo liên kết
   * @returns Danh sách liên kết đã tạo
   */
  async createTaskKrLinks(
    tenantId: number,
    createDto: CreateTaskKrDto,
  ): Promise<TaskKrResponseDto[]> {
    // Kiểm tra todo có tồn tại không
    const todo = await this.todoRepository.findById(tenantId, createDto.taskId);
    if (!todo) {
      throw new NotFoundException(`Không tìm thấy todo với ID ${createDto.taskId}`);
    }

    // TODO: Kiểm tra key results có tồn tại không (cần inject OKR service)
    // Hiện tại bỏ qua validation này để tránh circular dependency

    // Xóa các liên kết cũ của task này
    await this.taskKrRepository.deleteByTaskId(tenantId, createDto.taskId);

    // Tạo các liên kết mới
    const taskKrs = createDto.keyResultIds.map((krId) => ({
      taskId: createDto.taskId,
      krId,
    }));

    const createdTaskKrs = await this.taskKrRepository.createMany(tenantId, taskKrs);

    return createdTaskKrs.map(item => this.mapToResponseDto(item));
  }

  /**
   * Lấy danh sách key results của một task
   * @param tenantId ID tenant
   * @param taskId ID task
   * @returns Danh sách key results
   */
  async getTaskKeyResults(
    tenantId: number,
    taskId: number,
  ): Promise<TaskKrResponseDto[]> {
    // Kiểm tra todo có tồn tại không
    const todo = await this.todoRepository.findById(tenantId, taskId);
    if (!todo) {
      throw new NotFoundException(`Không tìm thấy todo với ID ${taskId}`);
    }

    const taskKrs = await this.taskKrRepository.findByTaskId(tenantId, taskId);
    return taskKrs.map(item => this.mapToResponseDto(item));
  }

  /**
   * Lấy danh sách tasks của một key result
   * @param tenantId ID tenant
   * @param krId ID key result
   * @returns Danh sách tasks
   */
  async getKeyResultTasks(
    tenantId: number,
    krId: number,
  ): Promise<TaskKrResponseDto[]> {
    // TODO: Kiểm tra key result có tồn tại không (cần inject OKR service)
    
    const taskKrs = await this.taskKrRepository.findByKrId(tenantId, krId);
    return taskKrs.map(item => this.mapToResponseDto(item));
  }

  /**
   * Xóa liên kết giữa task và key result
   * @param tenantId ID tenant
   * @param taskId ID task
   * @param krId ID key result
   * @returns Kết quả xóa
   */
  async deleteTaskKrLink(
    tenantId: number,
    taskId: number,
    krId: number,
  ): Promise<boolean> {
    // Kiểm tra liên kết có tồn tại không
    const taskKr = await this.taskKrRepository.findByTaskIdAndKrId(tenantId, taskId, krId);
    if (!taskKr) {
      throw new NotFoundException(`Không tìm thấy liên kết giữa task ${taskId} và key result ${krId}`);
    }

    return this.taskKrRepository.delete(tenantId, taskId, krId);
  }

  /**
   * Xóa tất cả liên kết của một task
   * @param tenantId ID tenant
   * @param taskId ID task
   * @returns Kết quả xóa
   */
  async deleteAllTaskLinks(
    tenantId: number,
    taskId: number,
  ): Promise<boolean> {
    // Kiểm tra todo có tồn tại không
    const todo = await this.todoRepository.findById(tenantId, taskId);
    if (!todo) {
      throw new NotFoundException(`Không tìm thấy todo với ID ${taskId}`);
    }

    return this.taskKrRepository.deleteByTaskId(tenantId, taskId);
  }

  /**
   * Chuyển đổi entity sang response DTO
   * @param taskKr Entity task-kr
   * @returns Response DTO
   */
  private mapToResponseDto(taskKr: any): TaskKrResponseDto {
    return {
      taskId: taskKr.taskId,
      krId: taskKr.krId,
      tenantId: taskKr.tenantId,
    };
  }
}
