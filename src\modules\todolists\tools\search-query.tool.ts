import { Injectable } from '@nestjs/common';
import { TodoService } from '../services/todo.service';
import { ProjectService } from '../services/project.service';
import { CampaignService } from '../services/campaign.service';

/**
 * Tool cho AI agent t<PERSON><PERSON> kiếm và query dữ liệu
 */
@Injectable()
export class SearchQueryTool {
  constructor(
    private readonly todoService: TodoService,
    private readonly projectService: ProjectService,
    private readonly campaignService: CampaignService,
  ) {}

  /**
   * Tìm kiếm todos
   */
  async searchTodos(params: {
    tenantId: number;
    query: string;
    filters?: {
      status?: string[];
      priority?: string[];
      assigneeIds?: number[];
      projectIds?: number[];
      hasDeadline?: boolean;
      isOverdue?: boolean;
      createdFrom?: string;
      createdTo?: string;
    };
    page?: number;
    limit?: number;
  }) {
    const searchDto = {
      keyword: params.query,
      statuses: params.filters?.status,
      priorities: params.filters?.priority,
      assigneeIds: params.filters?.assigneeIds,
      categoryIds: params.filters?.projectIds,
      hasDeadline: params.filters?.hasDeadline,
      isOverdue: params.filters?.isOverdue,
      createdFrom: params.filters?.createdFrom,
      createdTo: params.filters?.createdTo,
      page: params.page || 1,
      limit: params.limit || 10,
    };

    return this.todoService.advancedSearchTodos(params.tenantId, searchDto);
  }

  /**
   * Tìm kiếm projects
   */
  async searchProjects(params: {
    tenantId: number;
    query: string;
    filters?: {
      ownerId?: number;
      isActive?: boolean;
    };
    page?: number;
    limit?: number;
  }) {
    const searchDto = {
      keyword: params.query,
      ownerId: params.filters?.ownerId,
      isActive: params.filters?.isActive,
      page: params.page || 1,
      limit: params.limit || 10,
    };

    return this.projectService.findAllProjects(params.tenantId, searchDto);
  }

  /**
   * Tìm kiếm campaigns
   */
  async searchCampaigns(params: {
    tenantId: number;
    query: string;
    filters?: {
      status?: string;
      priority?: string;
      ownerId?: number;
      tags?: string[];
    };
    page?: number;
    limit?: number;
  }) {
    const searchDto = {
      keyword: params.query,
      status: params.filters?.status,
      priority: params.filters?.priority,
      ownerId: params.filters?.ownerId,
      tags: params.filters?.tags,
      page: params.page || 1,
      limit: params.limit || 10,
    };

    return this.campaignService.findAllCampaigns(params.tenantId, searchDto);
  }

  /**
   * Tìm kiếm tổng hợp
   */
  async globalSearch(params: {
    tenantId: number;
    query: string;
    types?: ('todos' | 'projects' | 'campaigns')[];
    limit?: number;
  }) {
    const searchTypes = params.types || ['todos', 'projects', 'campaigns'];
    const limit = params.limit || 5;
    const results: any = {};

    if (searchTypes.includes('todos')) {
      results.todos = await this.searchTodos({
        tenantId: params.tenantId,
        query: params.query,
        limit,
      });
    }

    if (searchTypes.includes('projects')) {
      results.projects = await this.searchProjects({
        tenantId: params.tenantId,
        query: params.query,
        limit,
      });
    }

    if (searchTypes.includes('campaigns')) {
      results.campaigns = await this.searchCampaigns({
        tenantId: params.tenantId,
        query: params.query,
        limit,
      });
    }

    return {
      query: params.query,
      results,
      totalResults: Object.values(results).reduce((sum: number, result: any) => 
        sum + (result?.meta?.totalItems || 0), 0
      ),
    };
  }

  /**
   * Tìm kiếm theo user
   */
  async searchByUser(params: {
    tenantId: number;
    userId: number;
    type: 'assigned' | 'created' | 'owned';
    entityType?: 'todos' | 'projects' | 'campaigns';
    page?: number;
    limit?: number;
  }) {
    const results: any = {};

    if (!params.entityType || params.entityType === 'todos') {
      const todoFilters: any = {};
      if (params.type === 'assigned') {
        todoFilters.assigneeId = params.userId;
      } else if (params.type === 'created') {
        todoFilters.createdBy = params.userId;
      }

      results.todos = await this.todoService.findAllTodos(params.tenantId, {
        ...todoFilters,
        page: params.page || 1,
        limit: params.limit || 10,
      });
    }

    if (!params.entityType || params.entityType === 'projects') {
      const projectFilters: any = {};
      if (params.type === 'owned') {
        projectFilters.ownerId = params.userId;
      }

      results.projects = await this.projectService.findAllProjects(params.tenantId, {
        ...projectFilters,
        page: params.page || 1,
        limit: params.limit || 10,
      });
    }

    if (!params.entityType || params.entityType === 'campaigns') {
      const campaignFilters: any = {};
      if (params.type === 'owned') {
        campaignFilters.ownerId = params.userId;
      } else if (params.type === 'created') {
        campaignFilters.createdBy = params.userId;
      }

      results.campaigns = await this.campaignService.findAllCampaigns(params.tenantId, {
        ...campaignFilters,
        page: params.page || 1,
        limit: params.limit || 10,
      });
    }

    return results;
  }

  /**
   * Tìm kiếm theo ngày
   */
  async searchByDate(params: {
    tenantId: number;
    dateType: 'created' | 'updated' | 'deadline' | 'completed';
    startDate: string;
    endDate: string;
    entityType?: 'todos' | 'projects' | 'campaigns';
    page?: number;
    limit?: number;
  }) {
    const results: any = {};

    if (!params.entityType || params.entityType === 'todos') {
      const filters: any = {};
      
      if (params.dateType === 'created') {
        filters.createdFrom = params.startDate;
        filters.createdTo = params.endDate;
      } else if (params.dateType === 'deadline') {
        filters.deadlineFrom = params.startDate;
        filters.deadlineTo = params.endDate;
      }

      results.todos = await this.todoService.advancedSearchTodos(params.tenantId, {
        ...filters,
        page: params.page || 1,
        limit: params.limit || 10,
      });
    }

    if (!params.entityType || params.entityType === 'campaigns') {
      const filters: any = {};
      
      if (params.dateType === 'created') {
        filters.startDateFrom = params.startDate;
        filters.startDateTo = params.endDate;
      }

      results.campaigns = await this.campaignService.findAllCampaigns(params.tenantId, {
        ...filters,
        page: params.page || 1,
        limit: params.limit || 10,
      });
    }

    return results;
  }

  /**
   * Lấy suggestions cho search
   */
  async getSearchSuggestions(params: {
    tenantId: number;
    query: string;
    type?: 'todos' | 'projects' | 'campaigns';
  }) {
    // TODO: Implement search suggestions based on existing data
    return {
      suggestions: [
        `${params.query} completed`,
        `${params.query} in progress`,
        `${params.query} high priority`,
        `${params.query} overdue`,
      ],
      recentSearches: [],
      popularSearches: [],
    };
  }
}
