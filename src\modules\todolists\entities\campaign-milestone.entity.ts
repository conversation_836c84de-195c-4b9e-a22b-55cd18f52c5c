import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity cho Campaign Milestone (Cột mốc chiến dịch)
 */
@Entity('campaign_milestone')
export class CampaignMilestone {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID chiến dịch
   */
  @Column({ type: 'int', nullable: false })
  campaignId: number;

  /**
   * ID giai đoạn (nếu thuộc về một giai đoạn cụ thể)
   */
  @Column({ type: 'int', nullable: true })
  phaseId: number | null;

  /**
   * Tên cột mốc
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * Mô tả cột mốc
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * <PERSON><PERSON><PERSON> đ<PERSON> (timestamp)
   */
  @Column({ type: 'bigint', nullable: false })
  dueDate: number;

  /**
   * <PERSON><PERSON><PERSON> ho<PERSON><PERSON> thành thực tế (timestamp)
   */
  @Column({ type: 'bigint', nullable: true })
  completedDate: number | null;

  /**
   * Trạng thái cột mốc
   */
  @Column({ 
    type: 'enum', 
    enum: ['pending', 'in_progress', 'completed', 'overdue', 'cancelled'],
    default: 'pending'
  })
  status: 'pending' | 'in_progress' | 'completed' | 'overdue' | 'cancelled';

  /**
   * Mức độ quan trọng
   */
  @Column({ 
    type: 'enum', 
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  })
  importance: 'low' | 'medium' | 'high' | 'critical';

  /**
   * Loại cột mốc
   */
  @Column({ 
    type: 'enum', 
    enum: ['deliverable', 'review', 'approval', 'launch', 'checkpoint'],
    default: 'checkpoint'
  })
  type: 'deliverable' | 'review' | 'approval' | 'launch' | 'checkpoint';

  /**
   * Tiêu chí hoàn thành
   */
  @Column({ type: 'text', nullable: true })
  completionCriteria: string | null;

  /**
   * Kết quả đạt được
   */
  @Column({ type: 'text', nullable: true })
  actualResults: string | null;

  /**
   * ID người phụ trách
   */
  @Column({ type: 'int', nullable: true })
  assigneeId: number | null;

  /**
   * Danh sách người liên quan
   */
  @Column({ type: 'json', nullable: true })
  stakeholders: number[] | null;

  /**
   * Phụ thuộc vào cột mốc nào (ID)
   */
  @Column({ type: 'json', nullable: true })
  dependencies: number[] | null;

  /**
   * Tài liệu đính kèm
   */
  @Column({ type: 'json', nullable: true })
  attachments: Array<{
    name: string;
    url: string;
    type: string;
    size: number;
  }> | null;

  /**
   * Metadata bổ sung
   */
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Có hoạt động hay không
   */
  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Thời gian tạo (timestamp)
   */
  @Column({ type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối (timestamp)
   */
  @Column({ type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID tenant (required for tenant isolation)
   */
  @Column({ type: 'int', nullable: false })
  tenantId: number;
}
