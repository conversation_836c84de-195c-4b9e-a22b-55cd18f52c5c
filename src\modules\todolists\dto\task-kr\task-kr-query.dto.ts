import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query task-kr mapping
 */
export class TaskKrQueryDto extends QueryDto {
  /**
   * ID của task để lọc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của task để lọc',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID task phải là số nguyên' })
  @Min(1, { message: 'ID task phải lớn hơn 0' })
  taskId?: number;

  /**
   * ID của key result để lọc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của key result để lọc',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID key result phải là số nguyên' })
  @Min(1, { message: 'ID key result phải lớn hơn 0' })
  krId?: number;
}
