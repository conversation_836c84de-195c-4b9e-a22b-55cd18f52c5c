import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Campaign } from '../entities/campaign.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { CampaignQueryDto } from '../dto/campaign/campaign-query.dto';

/**
 * Repository cho entity Campaign
 */
@Injectable()
export class CampaignRepository {
  constructor(
    @InjectRepository(Campaign)
    private readonly repository: Repository<Campaign>,
  ) {}

  /**
   * Tạo campaign mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu campaign
   * @returns Campaign đã tạo
   */
  async create(
    tenantId: number,
    data: Partial<Campaign>,
  ): Promise<Campaign> {
    const campaign = this.repository.create({
      ...data,
      tenantId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    return this.repository.save(campaign);
  }

  /**
   * Tìm tất cả campaign với phân trang
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách campaign đã phân trang
   */
  async findAll(
    tenantId: number,
    query: CampaignQueryDto,
  ): Promise<PaginatedResult<Campaign>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
      priority,
      ownerId,
      createdBy,
      keyword,
      tags,
      channels,
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
      minBudget,
      maxBudget,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('campaign');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.where('campaign.tenantId = :tenantId', { tenantId });

    // Lọc theo trạng thái
    if (status) {
      queryBuilder.andWhere('campaign.status = :status', { status });
    }

    // Lọc theo mức độ ưu tiên
    if (priority) {
      queryBuilder.andWhere('campaign.priority = :priority', { priority });
    }

    // Lọc theo người sở hữu
    if (ownerId) {
      queryBuilder.andWhere('campaign.ownerId = :ownerId', { ownerId });
    }

    // Lọc theo người tạo
    if (createdBy) {
      queryBuilder.andWhere('campaign.createdBy = :createdBy', { createdBy });
    }

    // Tìm kiếm theo từ khóa
    if (keyword) {
      queryBuilder.andWhere(
        '(campaign.title LIKE :keyword OR campaign.description LIKE :keyword)',
        { keyword: `%${keyword}%` },
      );
    }

    // Lọc theo tags
    if (tags && tags.length > 0) {
      queryBuilder.andWhere(
        'JSON_OVERLAPS(campaign.tags, :tags)',
        { tags: JSON.stringify(tags) },
      );
    }

    // Lọc theo kênh truyền thông
    if (channels && channels.length > 0) {
      queryBuilder.andWhere(
        'JSON_OVERLAPS(campaign.channels, :channels)',
        { channels: JSON.stringify(channels) },
      );
    }

    // Lọc theo ngày bắt đầu
    if (startDateFrom) {
      const fromTimestamp = new Date(startDateFrom).getTime();
      queryBuilder.andWhere('campaign.startDate >= :startDateFrom', { startDateFrom: fromTimestamp });
    }

    if (startDateTo) {
      const toTimestamp = new Date(startDateTo).getTime();
      queryBuilder.andWhere('campaign.startDate <= :startDateTo', { startDateTo: toTimestamp });
    }

    // Lọc theo ngày kết thúc
    if (endDateFrom) {
      const fromTimestamp = new Date(endDateFrom).getTime();
      queryBuilder.andWhere('campaign.endDate >= :endDateFrom', { endDateFrom: fromTimestamp });
    }

    if (endDateTo) {
      const toTimestamp = new Date(endDateTo).getTime();
      queryBuilder.andWhere('campaign.endDate <= :endDateTo', { endDateTo: toTimestamp });
    }

    // Lọc theo ngân sách
    if (minBudget !== undefined) {
      queryBuilder.andWhere('campaign.budget >= :minBudget', { minBudget });
    }

    if (maxBudget !== undefined) {
      queryBuilder.andWhere('campaign.budget <= :maxBudget', { maxBudget });
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`campaign.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm campaign theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID campaign
   * @returns Campaign hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<Campaign | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Cập nhật campaign
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID campaign
   * @param data Dữ liệu cập nhật
   * @returns Campaign đã cập nhật
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<Campaign>,
  ): Promise<Campaign | null> {
    await this.repository.update(
      { id, tenantId },
      { ...data, updatedAt: Date.now() }
    );
    return this.findById(tenantId, id);
  }

  /**
   * Xóa campaign
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID campaign
   * @returns Kết quả xóa
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Kiểm tra quyền truy cập campaign
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID campaign
   * @param userId ID user
   * @returns True nếu có quyền truy cập
   */
  async canUserAccess(tenantId: number, id: number, userId: number): Promise<boolean> {
    const campaign = await this.repository.findOne({
      where: { id, tenantId },
    });
    
    if (!campaign) return false;
    
    // User có quyền truy cập nếu là owner hoặc creator
    return campaign.ownerId === userId || campaign.createdBy === userId;
  }

  /**
   * Lấy thống kê campaign
   * @param tenantId ID tenant (required for tenant isolation)
   * @param campaignId ID campaign
   * @returns Thống kê campaign
   */
  async getCampaignStats(tenantId: number, campaignId: number): Promise<{
    totalPhases: number;
    completedPhases: number;
    totalMilestones: number;
    completedMilestones: number;
    overdueMilestones: number;
  }> {
    // TODO: Implement khi có CampaignPhase và CampaignMilestone repositories
    return {
      totalPhases: 0,
      completedPhases: 0,
      totalMilestones: 0,
      completedMilestones: 0,
      overdueMilestones: 0,
    };
  }
}
