import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsDateString, IsArray, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho query analytics dự án
 */
export class ProjectAnalyticsQueryDto {
  /**
   * <PERSON><PERSON><PERSON> bắt đầu thống kê (ISO string)
   */
  @ApiProperty({
    description: '<PERSON><PERSON>y bắt đầu thống kê (ISO string)',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  /**
   * <PERSON><PERSON><PERSON> kết thúc thống kê (ISO string)
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kết thúc thống kê (ISO string)',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  /**
   * Danh sách ID thành viên để lọc
   */
  @ApiProperty({
    description: 'Danh sách ID thành viên để lọc',
    type: [Number],
    required: false,
    example: [1, 2, 3],
  })
  @IsOptional()
  @IsArray()
  @Type(() => Number)
  @IsInt({ each: true, message: 'ID thành viên phải là số nguyên' })
  @Min(1, { each: true, message: 'ID thành viên phải lớn hơn 0' })
  memberIds?: number[];

  /**
   * Bao gồm thống kê timeline
   */
  @ApiProperty({
    description: 'Bao gồm thống kê timeline',
    required: false,
    example: true,
    default: true,
  })
  @IsOptional()
  includeTimeline?: boolean = true;

  /**
   * Bao gồm thống kê thành viên
   */
  @ApiProperty({
    description: 'Bao gồm thống kê thành viên',
    required: false,
    example: true,
    default: true,
  })
  @IsOptional()
  includeMemberStats?: boolean = true;
}
