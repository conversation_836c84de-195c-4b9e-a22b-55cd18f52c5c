import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  Patch,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/constants/swagger.constant';
import { CampaignService } from '../services/campaign.service';
import {
  CreateCampaignDto,
  UpdateCampaignDto,
  CampaignResponseDto,
  CampaignQueryDto,
} from '../dto/campaign';

/**
 * Controller xử lý các API liên quan đến campaign
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(ApiResponseDto, CampaignResponseDto)
@Controller('/campaigns')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  /**
   * Tạo campaign mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo campaign mới' })
  @ApiResponse({
    status: 201,
    description: 'Campaign đã được tạo thành công',
    schema: ApiResponseDto.getSchema(CampaignResponseDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async createCampaign(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateCampaignDto,
  ): Promise<ApiResponseDto<CampaignResponseDto>> {
    const campaign = await this.campaignService.createCampaign(
      Number(user.tenantId),
      user.id,
      createDto,
    );
    return ApiResponseDto.created(campaign, 'Tạo campaign thành công');
  }

  /**
   * Lấy danh sách campaign với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách campaign với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách campaign',
    schema: ApiResponseDto.getPaginatedSchema(CampaignResponseDto),
  })
  async findAllCampaigns(
    @CurrentUser() user: JwtPayload,
    @Query() query: CampaignQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<CampaignResponseDto>>> {
    const paginatedCampaigns = await this.campaignService.findAllCampaigns(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(paginatedCampaigns, 'Lấy danh sách campaign thành công');
  }

  /**
   * Lấy campaign theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy campaign theo ID' })
  @ApiParam({ name: 'id', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin campaign',
    schema: ApiResponseDto.getSchema(CampaignResponseDto),
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy campaign',
  })
  async findCampaignById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<CampaignResponseDto>> {
    const campaign = await this.campaignService.findCampaignById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(campaign, 'Lấy thông tin campaign thành công');
  }

  /**
   * Cập nhật campaign
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật campaign' })
  @ApiParam({ name: 'id', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Campaign đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CampaignResponseDto),
  })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền cập nhật campaign này',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy campaign',
  })
  async updateCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCampaignDto,
  ): Promise<ApiResponseDto<CampaignResponseDto>> {
    const campaign = await this.campaignService.updateCampaign(
      Number(user.tenantId),
      id,
      user.id,
      updateDto,
    );
    return ApiResponseDto.success(campaign, 'Cập nhật campaign thành công');
  }

  /**
   * Xóa campaign
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa campaign' })
  @ApiParam({ name: 'id', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Campaign đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền xóa campaign này',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy campaign',
  })
  async deleteCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    await this.campaignService.deleteCampaign(
      Number(user.tenantId),
      id,
      user.id,
    );
    return ApiResponseDto.deleted('Xóa campaign thành công');
  }

  /**
   * Thay đổi trạng thái campaign
   */
  @Patch(':id/status')
  @ApiOperation({ summary: 'Thay đổi trạng thái campaign' })
  @ApiParam({ name: 'id', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Trạng thái campaign đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CampaignResponseDto),
  })
  async updateCampaignStatus(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled' },
  ): Promise<ApiResponseDto<CampaignResponseDto>> {
    const campaign = await this.campaignService.updateCampaignStatus(
      Number(user.tenantId),
      id,
      user.id,
      body.status,
    );
    return ApiResponseDto.success(campaign, 'Cập nhật trạng thái campaign thành công');
  }

  /**
   * Lấy thống kê campaign
   */
  @Get(':id/stats')
  @ApiOperation({ summary: 'Lấy thống kê campaign' })
  @ApiParam({ name: 'id', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thống kê campaign',
  })
  async getCampaignStats(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<any>> {
    const stats = await this.campaignService.getCampaignStats(
      Number(user.tenantId),
      id,
      user.id,
    );
    return ApiResponseDto.success(stats, 'Lấy thống kê campaign thành công');
  }
}
