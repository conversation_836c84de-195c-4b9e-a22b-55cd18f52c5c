import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query todo appreciation
 */
export class TodoAppreciationQueryDto extends QueryDto {
  /**
   * ID của todo để lọc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của todo để lọc',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID todo phải là số nguyên' })
  @Min(1, { message: 'ID todo phải lớn hơn 0' })
  todoId?: number;

  /**
   * ID của user để lọc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của user để lọc',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID user phải là số nguyên' })
  @Min(1, { message: 'ID user phải lớn hơn 0' })
  userId?: number;
}
