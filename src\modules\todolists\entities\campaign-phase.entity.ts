import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity cho Campaign Phase (Giai đoạn chiến dịch)
 */
@Entity('campaign_phase')
export class CampaignPhase {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID chiến dịch
   */
  @Column({ type: 'int', nullable: false })
  campaignId: number;

  /**
   * Tên giai đoạn
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * Mô tả giai đoạn
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Thứ tự giai đoạn
   */
  @Column({ type: 'int', nullable: false })
  order: number;

  /**
   * <PERSON><PERSON>y bắt đầu giai đoạn (timestamp)
   */
  @Column({ type: 'bigint', nullable: true })
  startDate: number | null;

  /**
   * <PERSON><PERSON><PERSON> kết thúc giai đoạn (timestamp)
   */
  @Column({ type: 'bigint', nullable: true })
  endDate: number | null;

  /**
   * Trạng thái giai đoạn
   */
  @Column({ 
    type: 'enum', 
    enum: ['pending', 'in_progress', 'completed', 'cancelled'],
    default: 'pending'
  })
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';

  /**
   * Mức độ ưu tiên
   */
  @Column({ 
    type: 'enum', 
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  })
  priority: 'low' | 'medium' | 'high' | 'urgent';

  /**
   * Ngân sách giai đoạn
   */
  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  budget: number | null;

  /**
   * Mục tiêu giai đoạn
   */
  @Column({ type: 'text', nullable: true })
  objectives: string | null;

  /**
   * Kết quả mong đợi
   */
  @Column({ type: 'text', nullable: true })
  expectedResults: string | null;

  /**
   * Tiêu chí hoàn thành
   */
  @Column({ type: 'text', nullable: true })
  completionCriteria: string | null;

  /**
   * ID người phụ trách giai đoạn
   */
  @Column({ type: 'int', nullable: true })
  assigneeId: number | null;

  /**
   * Phụ thuộc vào giai đoạn nào (ID)
   */
  @Column({ type: 'json', nullable: true })
  dependencies: number[] | null;

  /**
   * Metadata bổ sung
   */
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Có hoạt động hay không
   */
  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Thời gian tạo (timestamp)
   */
  @Column({ type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối (timestamp)
   */
  @Column({ type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID tenant (required for tenant isolation)
   */
  @Column({ type: 'int', nullable: false })
  tenantId: number;
}
