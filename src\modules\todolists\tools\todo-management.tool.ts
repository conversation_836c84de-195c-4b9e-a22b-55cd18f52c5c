import { Injectable } from '@nestjs/common';
import { TodoService } from '../services/todo.service';
import { CreateTodoDto } from '../dto/todo/create-todo.dto';
import { UpdateTodoDto } from '../dto/todo/update-todo.dto';
import { TodoQueryDto } from '../dto/todo/todo-query.dto';

/**
 * Tool cho AI agent quản lý todos
 */
@Injectable()
export class TodoManagementTool {
  constructor(private readonly todoService: TodoService) {}

  /**
   * Tạo todo mới
   */
  async createTodo(params: {
    tenantId: number;
    userId: number;
    title: string;
    description?: string;
    assigneeId?: number;
    categoryId?: number;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    deadline?: string;
    expectedStars?: number;
  }) {
    const createDto: CreateTodoDto = {
      title: params.title,
      description: params.description,
      assigneeId: params.assigneeId,
      categoryId: params.categoryId,
      priority: params.priority,
      deadline: params.deadline,
      expectedStars: params.expectedStars,
    };

    return this.todoService.createTodo(params.tenantId, params.userId, createDto);
  }

  /**
   * Cập nhật todo
   */
  async updateTodo(params: {
    tenantId: number;
    todoId: number;
    userId: number;
    title?: string;
    description?: string;
    status?: 'pending' | 'in_progress' | 'completed' | 'approved' | 'rejected';
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    assigneeId?: number;
    deadline?: string;
  }) {
    const updateDto: UpdateTodoDto = {
      title: params.title,
      description: params.description,
      status: params.status,
      priority: params.priority,
      assigneeId: params.assigneeId,
      deadline: params.deadline,
    };

    return this.todoService.updateTodo(params.tenantId, params.todoId, params.userId, updateDto);
  }

  /**
   * Gán todo cho user
   */
  async assignTodo(params: {
    tenantId: number;
    todoId: number;
    userId: number;
    assigneeId: number;
  }) {
    return this.todoService.updateTodoAssignee(
      params.tenantId,
      params.todoId,
      params.userId,
      { assigneeId: params.assigneeId }
    );
  }

  /**
   * Hoàn thành todo
   */
  async completeTodo(params: {
    tenantId: number;
    todoId: number;
    userId: number;
  }) {
    return this.todoService.updateTodoStatus(
      params.tenantId,
      params.todoId,
      params.userId,
      { status: 'completed' }
    );
  }

  /**
   * Lấy danh sách todos
   */
  async getTodos(params: {
    tenantId: number;
    page?: number;
    limit?: number;
    status?: 'pending' | 'in_progress' | 'completed' | 'approved' | 'rejected';
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    assigneeId?: number;
    categoryId?: number;
    keyword?: string;
  }) {
    const query: TodoQueryDto = {
      page: params.page || 1,
      limit: params.limit || 10,
      status: params.status,
      priority: params.priority,
      assigneeId: params.assigneeId,
      categoryId: params.categoryId,
      keyword: params.keyword,
    };

    return this.todoService.findAllTodos(params.tenantId, query);
  }

  /**
   * Lấy todo theo ID
   */
  async getTodoById(params: {
    tenantId: number;
    todoId: number;
  }) {
    return this.todoService.findTodoById(params.tenantId, params.todoId);
  }

  /**
   * Xóa todo
   */
  async deleteTodo(params: {
    tenantId: number;
    todoId: number;
    userId: number;
  }) {
    return this.todoService.deleteTodo(params.tenantId, params.todoId, params.userId);
  }

  /**
   * Bulk update todos
   */
  async bulkUpdateTodos(params: {
    tenantId: number;
    userId: number;
    todoIds: number[];
    status?: 'pending' | 'in_progress' | 'completed' | 'approved' | 'rejected';
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    assigneeId?: number;
  }) {
    const bulkUpdateDto = {
      ids: params.todoIds,
      status: params.status,
      priority: params.priority,
      assigneeId: params.assigneeId,
    };

    return this.todoService.bulkUpdateTodos(params.tenantId, params.userId, bulkUpdateDto);
  }

  /**
   * Tìm kiếm todos nâng cao
   */
  async searchTodos(params: {
    tenantId: number;
    keyword?: string;
    statuses?: string[];
    priorities?: string[];
    assigneeIds?: number[];
    categoryIds?: number[];
    hasDeadline?: boolean;
    isOverdue?: boolean;
    createdFrom?: string;
    createdTo?: string;
    deadlineFrom?: string;
    deadlineTo?: string;
  }) {
    const searchDto = {
      keyword: params.keyword,
      statuses: params.statuses,
      priorities: params.priorities,
      assigneeIds: params.assigneeIds,
      categoryIds: params.categoryIds,
      hasDeadline: params.hasDeadline,
      isOverdue: params.isOverdue,
      createdFrom: params.createdFrom,
      createdTo: params.createdTo,
      deadlineFrom: params.deadlineFrom,
      deadlineTo: params.deadlineTo,
    };

    return this.todoService.advancedSearchTodos(params.tenantId, searchDto);
  }

  /**
   * Lấy thống kê todos
   */
  async getTodoStats(params: {
    tenantId: number;
    userId?: number;
    categoryId?: number;
    startDate?: string;
    endDate?: string;
  }) {
    // TODO: Implement khi có method getTodoStats trong TodoService
    return {
      total: 0,
      completed: 0,
      inProgress: 0,
      pending: 0,
      overdue: 0,
      completionRate: 0,
    };
  }
}
