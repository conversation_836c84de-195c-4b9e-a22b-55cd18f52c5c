import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray, IsEnum, IsInt, Min, IsDateString, IsBoolean } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { TodoStatus } from '../../enum/todo-status.enum';
import { TodoPriority } from '../../enum/todo-priority.enum';

/**
 * DTO cho tìm kiếm nâng cao công việc
 */
export class AdvancedSearchTodoDto extends QueryDto {
  /**
   * Từ khóa tìm kiếm trong tiêu đề và mô tả
   */
  @ApiProperty({
    description: 'Từ khóa tìm kiếm trong tiêu đề và mô tả',
    required: false,
    example: 'báo cáo',
  })
  @IsOptional()
  @IsString()
  keyword?: string;

  /**
   * <PERSON>h sách trạng thái để lọc
   */
  @ApiProperty({
    description: 'Danh sách trạng thái để lọc',
    enum: TodoStatus,
    isArray: true,
    required: false,
    example: [TodoStatus.PENDING, TodoStatus.IN_PROGRESS],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(TodoStatus, { each: true, message: 'Trạng thái không hợp lệ' })
  statuses?: TodoStatus[];

  /**
   * Danh sách mức độ ưu tiên để lọc
   */
  @ApiProperty({
    description: 'Danh sách mức độ ưu tiên để lọc',
    enum: TodoPriority,
    isArray: true,
    required: false,
    example: [TodoPriority.HIGH, TodoPriority.URGENT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(TodoPriority, { each: true, message: 'Mức độ ưu tiên không hợp lệ' })
  priorities?: TodoPriority[];

  /**
   * Danh sách ID người được giao để lọc
   */
  @ApiProperty({
    description: 'Danh sách ID người được giao để lọc',
    type: [Number],
    required: false,
    example: [1, 2, 3],
  })
  @IsOptional()
  @IsArray()
  @Type(() => Number)
  @IsInt({ each: true, message: 'ID người được giao phải là số nguyên' })
  @Min(1, { each: true, message: 'ID người được giao phải lớn hơn 0' })
  assigneeIds?: number[];

  /**
   * Danh sách ID dự án để lọc
   */
  @ApiProperty({
    description: 'Danh sách ID dự án để lọc',
    type: [Number],
    required: false,
    example: [1, 2],
  })
  @IsOptional()
  @IsArray()
  @Type(() => Number)
  @IsInt({ each: true, message: 'ID dự án phải là số nguyên' })
  @Min(1, { each: true, message: 'ID dự án phải lớn hơn 0' })
  categoryIds?: number[];

  /**
   * Danh sách ID tag để lọc
   */
  @ApiProperty({
    description: 'Danh sách ID tag để lọc',
    type: [Number],
    required: false,
    example: [1, 2, 3],
  })
  @IsOptional()
  @IsArray()
  @Type(() => Number)
  @IsInt({ each: true, message: 'ID tag phải là số nguyên' })
  @Min(1, { each: true, message: 'ID tag phải lớn hơn 0' })
  tagIds?: number[];

  /**
   * Ngày tạo từ (ISO string)
   */
  @ApiProperty({
    description: 'Ngày tạo từ (ISO string)',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  createdFrom?: string;

  /**
   * Ngày tạo đến (ISO string)
   */
  @ApiProperty({
    description: 'Ngày tạo đến (ISO string)',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  createdTo?: string;

  /**
   * Deadline từ (ISO string)
   */
  @ApiProperty({
    description: 'Deadline từ (ISO string)',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  deadlineFrom?: string;

  /**
   * Deadline đến (ISO string)
   */
  @ApiProperty({
    description: 'Deadline đến (ISO string)',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  deadlineTo?: string;

  /**
   * Số sao kỳ vọng tối thiểu
   */
  @ApiProperty({
    description: 'Số sao kỳ vọng tối thiểu',
    required: false,
    example: 3,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Số sao kỳ vọng tối thiểu phải là số nguyên' })
  @Min(1, { message: 'Số sao kỳ vọng tối thiểu phải từ 1 đến 5' })
  minExpectedStars?: number;

  /**
   * Số sao kỳ vọng tối đa
   */
  @ApiProperty({
    description: 'Số sao kỳ vọng tối đa',
    required: false,
    example: 5,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Số sao kỳ vọng tối đa phải là số nguyên' })
  @Min(1, { message: 'Số sao kỳ vọng tối đa phải từ 1 đến 5' })
  maxExpectedStars?: number;

  /**
   * Chỉ lấy công việc có deadline
   */
  @ApiProperty({
    description: 'Chỉ lấy công việc có deadline',
    required: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  hasDeadline?: boolean;

  /**
   * Chỉ lấy công việc đã quá hạn
   */
  @ApiProperty({
    description: 'Chỉ lấy công việc đã quá hạn',
    required: false,
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isOverdue?: boolean;

  /**
   * Chỉ lấy công việc có subtasks
   */
  @ApiProperty({
    description: 'Chỉ lấy công việc có subtasks',
    required: false,
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  hasSubtasks?: boolean;
}
