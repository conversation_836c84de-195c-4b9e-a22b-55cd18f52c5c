import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của campaign
 */
export class CampaignResponseDto {
  /**
   * ID chiến dịch
   */
  @ApiProperty({
    description: 'ID chiến dịch',
    example: 1,
  })
  id: number;

  /**
   * Tên chiến dịch
   */
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch Marketing Q4 2024',
  })
  title: string;

  /**
   * <PERSON><PERSON> tả chiến dịch
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả chiến dịch',
    example: 'Chiến dịch marketing tổng thể cho quý 4 năm 2024',
    nullable: true,
  })
  description: string | null;

  /**
   * ID người tạo chiến dịch
   */
  @ApiProperty({
    description: 'ID người tạo chiến dịch',
    example: 1,
  })
  createdBy: number;

  /**
   * ID người sở hữu chiến dịch
   */
  @ApiProperty({
    description: 'ID người sở hữu chiến dịch',
    example: 1,
  })
  ownerId: number;

  /**
   * <PERSON><PERSON><PERSON> bắt đầu chiến dịch (timestamp)
   */
  @ApiProperty({
    description: 'Ngày bắt đầu chiến dịch (timestamp)',
    example: 1696118400000,
    nullable: true,
  })
  startDate: number | null;

  /**
   * Ngày kết thúc chiến dịch (timestamp)
   */
  @ApiProperty({
    description: 'Ngày kết thúc chiến dịch (timestamp)',
    example: 1704067199000,
    nullable: true,
  })
  endDate: number | null;

  /**
   * Trạng thái chiến dịch
   */
  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ['draft', 'active', 'paused', 'completed', 'cancelled'],
    example: 'active',
  })
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';

  /**
   * Mức độ ưu tiên
   */
  @ApiProperty({
    description: 'Mức độ ưu tiên',
    enum: ['low', 'medium', 'high', 'urgent'],
    example: 'high',
  })
  priority: 'low' | 'medium' | 'high' | 'urgent';

  /**
   * Ngân sách chiến dịch
   */
  @ApiProperty({
    description: 'Ngân sách chiến dịch',
    example: 1000000000,
    nullable: true,
  })
  budget: number | null;

  /**
   * Đơn vị tiền tệ
   */
  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    nullable: true,
  })
  currency: string | null;

  /**
   * Mục tiêu chiến dịch
   */
  @ApiProperty({
    description: 'Mục tiêu chiến dịch',
    example: 'Tăng nhận diện thương hiệu và doanh số bán hàng',
    nullable: true,
  })
  objectives: string | null;

  /**
   * Nhóm mục tiêu
   */
  @ApiProperty({
    description: 'Nhóm mục tiêu',
    example: 'Khách hàng từ 25-40 tuổi, thu nhập trung bình khá',
    nullable: true,
  })
  targetAudience: string | null;

  /**
   * Kênh truyền thông
   */
  @ApiProperty({
    description: 'Kênh truyền thông',
    example: ['facebook', 'google-ads', 'email', 'website'],
    nullable: true,
    type: [String],
  })
  channels: string[] | null;

  /**
   * Tags/nhãn
   */
  @ApiProperty({
    description: 'Tags/nhãn',
    example: ['marketing', 'q4-2024', 'brand-awareness'],
    nullable: true,
    type: [String],
  })
  tags: string[] | null;

  /**
   * Metadata bổ sung
   */
  @ApiProperty({
    description: 'Metadata bổ sung',
    example: { customField1: 'value1', customField2: 'value2' },
    nullable: true,
  })
  metadata: Record<string, any> | null;

  /**
   * Có hoạt động hay không
   */
  @ApiProperty({
    description: 'Có hoạt động hay không',
    example: true,
  })
  isActive: boolean;

  /**
   * Thời gian tạo (timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối (timestamp)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1640995200000,
  })
  updatedAt: number;

  /**
   * ID tenant
   */
  @ApiProperty({
    description: 'ID tenant',
    example: 1,
  })
  tenantId: number;
}
