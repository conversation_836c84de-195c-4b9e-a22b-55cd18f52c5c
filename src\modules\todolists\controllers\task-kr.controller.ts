import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/constants/swagger.constant';
import { TaskKrService } from '../services/task-kr.service';
import {
  CreateTaskKrDto,
  TaskKrResponseDto,
} from '../dto/task-kr';

/**
 * Controller xử lý các API liên quan đến task-kr mapping (OKR integration)
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(ApiResponseDto, TaskKrResponseDto)
@Controller('/todos/okr-integration')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class TaskKrController {
  constructor(private readonly taskKrService: TaskKrService) {}

  /**
   * Tạo liên kết giữa task và key results
   */
  @Post()
  @ApiOperation({ summary: 'Tạo liên kết giữa task và key results' })
  @ApiResponse({
    status: 201,
    description: 'Liên kết đã được tạo thành công',
    schema: ApiResponseDto.getArraySchema(TaskKrResponseDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy todo hoặc key result',
  })
  async createTaskKrLinks(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateTaskKrDto,
  ): Promise<ApiResponseDto<TaskKrResponseDto[]>> {
    const taskKrs = await this.taskKrService.createTaskKrLinks(
      Number(user.tenantId),
      createDto,
    );
    return ApiResponseDto.created(taskKrs, 'Tạo liên kết OKR thành công');
  }

  /**
   * Lấy danh sách key results của một task
   */
  @Get('tasks/:taskId/key-results')
  @ApiOperation({ summary: 'Lấy danh sách key results của một task' })
  @ApiParam({ name: 'taskId', description: 'ID task', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách key results của task',
    schema: ApiResponseDto.getArraySchema(TaskKrResponseDto),
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy task',
  })
  async getTaskKeyResults(
    @CurrentUser() user: JwtPayload,
    @Param('taskId', ParseIntPipe) taskId: number,
  ): Promise<ApiResponseDto<TaskKrResponseDto[]>> {
    const taskKrs = await this.taskKrService.getTaskKeyResults(
      Number(user.tenantId),
      taskId,
    );
    return ApiResponseDto.success(taskKrs, 'Lấy danh sách key results thành công');
  }

  /**
   * Lấy danh sách tasks của một key result
   */
  @Get('key-results/:krId/tasks')
  @ApiOperation({ summary: 'Lấy danh sách tasks của một key result' })
  @ApiParam({ name: 'krId', description: 'ID key result', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tasks của key result',
    schema: ApiResponseDto.getArraySchema(TaskKrResponseDto),
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy key result',
  })
  async getKeyResultTasks(
    @CurrentUser() user: JwtPayload,
    @Param('krId', ParseIntPipe) krId: number,
  ): Promise<ApiResponseDto<TaskKrResponseDto[]>> {
    const taskKrs = await this.taskKrService.getKeyResultTasks(
      Number(user.tenantId),
      krId,
    );
    return ApiResponseDto.success(taskKrs, 'Lấy danh sách tasks thành công');
  }

  /**
   * Xóa liên kết giữa task và key result
   */
  @Delete('tasks/:taskId/key-results/:krId')
  @ApiOperation({ summary: 'Xóa liên kết giữa task và key result' })
  @ApiParam({ name: 'taskId', description: 'ID task', type: 'number' })
  @ApiParam({ name: 'krId', description: 'ID key result', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Liên kết đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy liên kết',
  })
  async deleteTaskKrLink(
    @CurrentUser() user: JwtPayload,
    @Param('taskId', ParseIntPipe) taskId: number,
    @Param('krId', ParseIntPipe) krId: number,
  ): Promise<ApiResponseDto<boolean>> {
    await this.taskKrService.deleteTaskKrLink(
      Number(user.tenantId),
      taskId,
      krId,
    );
    return ApiResponseDto.deleted('Xóa liên kết OKR thành công');
  }

  /**
   * Xóa tất cả liên kết của một task
   */
  @Delete('tasks/:taskId/key-results')
  @ApiOperation({ summary: 'Xóa tất cả liên kết OKR của một task' })
  @ApiParam({ name: 'taskId', description: 'ID task', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Tất cả liên kết đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy task',
  })
  async deleteAllTaskLinks(
    @CurrentUser() user: JwtPayload,
    @Param('taskId', ParseIntPipe) taskId: number,
  ): Promise<ApiResponseDto<boolean>> {
    await this.taskKrService.deleteAllTaskLinks(
      Number(user.tenantId),
      taskId,
    );
    return ApiResponseDto.deleted('Xóa tất cả liên kết OKR thành công');
  }
}
