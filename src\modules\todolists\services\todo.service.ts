import { Injectable, Logger } from '@nestjs/common';
import { TodoRepository } from '../repositories/todo.repository';
import { ProjectMemberRepository } from '../repositories/project-member.repository';
import { TaskKrRepository } from '../repositories/task-kr.repository';
import { TodoScoreRepository } from '../repositories/todo-score.repository';
import { Todo } from '../entities/todo.entity';
import { TodoScore } from '../entities/todo-score.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { CreateTodoDto } from '../dto/todo/create-todo.dto';
import { UpdateTodoDto } from '../dto/todo/update-todo.dto';
import { TodoQueryDto } from '../dto/todo/todo-query.dto';
import { UpdateTodoStatusDto } from '../dto/todo/update-todo-status.dto';
import { BulkDeleteTodoDto } from '../dto/todo/bulk-delete-todo.dto';
import { BulkDeleteTodoResponseDto } from '../dto/todo/bulk-delete-todo-response.dto';
import { BulkUpdateTodoDto } from '../dto/todo/bulk-update-todo.dto';
import { BulkUpdateTodoResponseDto } from '../dto/todo/bulk-update-todo-response.dto';
import { AdvancedSearchTodoDto } from '../dto/todo/advanced-search-todo.dto';
import { ScoreTodoDto } from '../dto/todo-score/score-todo.dto';
import { TodoResponseDto } from '../dto/todo/todo-response.dto';
import { AssigneeInfoDto } from '../dto/todo/assignee-info.dto';
import { SetDeadlineDto } from '../dto/todo/set-deadline.dto';
import { DeadlineStatisticsQueryDto } from '../dto/todo/deadline-statistics-query.dto';
import {
  DeadlineStatisticsResponseDto,
  DeadlineTodoItemDto,
} from '../dto/todo/deadline-statistics-response.dto';
import { TodoStatus } from '../enum/todo-status.enum';
import { TodoPriority } from '../enum/todo-priority.enum';
import { ProjectMemberRole } from '../enum/project-member-role.enum';

/**
 * Service xử lý logic nghiệp vụ cho công việc
 */
@Injectable()
export class TodoService {
  private readonly logger = new Logger(TodoService.name);

  constructor(
    private readonly todoRepository: TodoRepository,
    private readonly projectMemberRepository: ProjectMemberRepository,
    private readonly taskKrRepository: TaskKrRepository,
    private readonly todoScoreRepository: TodoScoreRepository,
  ) {}

  /**
   * Map từ Todo entity sang TodoResponseDto
   * @param todo Todo entity
   * @returns TodoResponseDto
   */
  private async mapToResponseDto(todo: Todo): Promise<TodoResponseDto> {
    // Lấy thông tin assignee nếu có
    let assignee: AssigneeInfoDto | null = null;
    if (todo.assigneeId) {
      try {
        // Sử dụng query trực tiếp để lấy thông tin employee
        const assigneeInfo = await this.todoRepository.createQueryBuilder('todo')
          .leftJoin('employees', 'emp', 'emp.id = :assigneeId AND emp.tenant_id = :tenantId')
          .select([
            'emp.id as id',
            'emp.employee_name as name',
            'emp.email as email',
            'emp.employee_code as employeeCode'
          ])
          .where('emp.id = :assigneeId AND emp.tenant_id = :tenantId')
          .setParameters({ assigneeId: todo.assigneeId, tenantId: todo.tenantId })
          .getRawOne();

        if (assigneeInfo) {
          assignee = {
            id: assigneeInfo.id,
            name: assigneeInfo.name,
            email: assigneeInfo.email,
            employeeCode: assigneeInfo.employeeCode,
          };
        }
      } catch (error) {
        this.logger.warn(`Không thể lấy thông tin assignee ${todo.assigneeId}: ${error.message}`);
      }
    }

    return {
      id: todo.id,
      title: todo.title,
      description: todo.description,
      assigneeId: todo.assigneeId,
      assignee: assignee,
      status: todo.status,
      priority: todo.priority,
      expectedStars: todo.expectedStars,
      awardedStars: todo.awardedStars,
      createdBy: todo.createdBy,
      createdAt: todo.createdAt,
      updatedAt: todo.updatedAt,
      startedAt: todo.startedAt,
      completedAt: todo.completedAt,
      deadline: todo.deadline,
      categoryId: todo.categoryId,
      parentId: todo.parentId,
      tags: null, // Sẽ được set riêng khi cần
    };
  }

  /**
   * Tạo công việc mới
   * @param tenantId ID tenant
   * @param userId ID người dùng tạo công việc
   * @param createTodoDto Thông tin công việc
   * @returns Công việc đã tạo
   */
  async createTodo(
    tenantId: number,
    userId: number,
    createTodoDto: CreateTodoDto,
  ): Promise<TodoResponseDto> {
    try {
      // Kiểm tra công việc cha nếu có
      if (createTodoDto.parentId) {
        const parentTodo = await this.todoRepository.findById(
          tenantId,
          createTodoDto.parentId,
        );
        if (!parentTodo) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.PARENT_TODO_NOT_FOUND,
            `Không tìm thấy công việc cha với ID ${createTodoDto.parentId}`,
          );
        }
      }

      // Kiểm tra quyền tạo công việc trong dự án nếu có
      if (createTodoDto.categoryId) {
        const isMember = await this.projectMemberRepository.isProjectMember(
          tenantId,
          createTodoDto.categoryId,
          userId,
        );
        if (!isMember) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.NOT_PROJECT_MEMBER,
            'Bạn không phải là thành viên của dự án này',
          );
        }
      }

      // Tạo công việc mới
      const now = Date.now();
      const todo = await this.todoRepository.create(tenantId, {
        title: createTodoDto.title,
        description: createTodoDto.description,
        assigneeId: createTodoDto.assigneeId,
        status: TodoStatus.PENDING,
        priority: createTodoDto.priority,
        expectedStars: createTodoDto.expectedStars || 3,
        createdBy: userId,
        createdAt: now,
        updatedAt: now,
        startedAt: createTodoDto.startedAt || null,
        categoryId: createTodoDto.categoryId,
        parentId: createTodoDto.parentId,
        deadline: createTodoDto.deadline,
      });

      // Tạo liên kết với key result nếu có
      if (createTodoDto.keyResultIds && createTodoDto.keyResultIds.length > 0) {
        const taskKrs = createTodoDto.keyResultIds.map((krId) => ({
          taskId: todo.id,
          krId,
        }));
        await this.taskKrRepository.createMany(tenantId, taskKrs);
      }

      return await this.mapToResponseDto(todo);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo công việc: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_CREATION_FAILED,
        `Tạo công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách công việc
   * @param tenantId ID tenant
   * @param query Tham số truy vấn
   * @returns Danh sách công việc đã phân trang với tags
   */
  async findAllTodos(
    tenantId: number,
    query: TodoQueryDto,
  ): Promise<PaginatedResult<any>> {
    try {
      const result = await this.todoRepository.findAllWithTags(tenantId, query);

      // Map kết quả để đảm bảo tags có giá trị null nếu không có tags
      const mappedItems = result.items.map((item) => ({
        ...item,
        tags: item.tags && item.tags.length > 0 ? item.tags : null,
      }));

      return {
        items: mappedItems,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy danh sách công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết công việc (entity) - dùng nội bộ
   * @param tenantId ID tenant
   * @param id ID công việc
   * @returns Todo entity
   */
  private async findTodoEntityById(
    tenantId: number,
    id: number,
  ): Promise<Todo> {
    const todo = await this.todoRepository.findById(tenantId, id);
    if (!todo) {
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Không tìm thấy công việc với ID ${id}`,
      );
    }
    return todo;
  }

  /**
   * Lấy chi tiết công việc
   * @param tenantId ID tenant
   * @param id ID công việc
   * @returns Thông tin chi tiết công việc
   */
  async findTodoById(tenantId: number, id: number): Promise<TodoResponseDto> {
    const todo = await this.findTodoEntityById(tenantId, id);
    return await this.mapToResponseDto(todo);
  }

  /**
   * Lấy danh sách công việc con
   * @param tenantId ID tenant
   * @param parentId ID công việc cha
   * @param query Tham số truy vấn
   * @returns Danh sách công việc con đã phân trang
   */
  async findSubtasks(
    tenantId: number,
    parentId: number,
    query: TodoQueryDto,
  ): Promise<PaginatedResult<TodoResponseDto>> {
    try {
      // Kiểm tra công việc cha tồn tại
      await this.findTodoEntityById(tenantId, parentId);

      // Lấy danh sách công việc con
      const result = await this.todoRepository.findSubtasks(
        tenantId,
        parentId,
        query,
      );

      // Map kết quả sang TodoResponseDto
      const mappedItems = await Promise.all(
        result.items.map((item) => this.mapToResponseDto(item))
      );

      return {
        items: mappedItems,
        meta: result.meta,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách công việc con: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy danh sách công việc con thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param userId ID người dùng cập nhật
   * @param updateTodoDto Thông tin cập nhật
   * @returns Công việc đã cập nhật
   */
  async updateTodo(
    tenantId: number,
    id: number,
    userId: number,
    updateTodoDto: UpdateTodoDto,
  ): Promise<TodoResponseDto | null> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoEntityById(tenantId, id);

      // Kiểm tra quyền cập nhật (chỉ người được giao hoặc người tạo mới có quyền cập nhật)
      if (todo.assigneeId !== userId && todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Bạn không có quyền cập nhật công việc này',
        );
      }

      // Kiểm tra dự án nếu có thay đổi
      if (
        updateTodoDto.categoryId &&
        updateTodoDto.categoryId !== todo.categoryId
      ) {
        const isMember = await this.projectMemberRepository.isProjectMember(
          tenantId,
          updateTodoDto.categoryId,
          userId,
        );
        if (!isMember) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.NOT_PROJECT_MEMBER,
            'Bạn không phải là thành viên của dự án này',
          );
        }
      }

      // Cập nhật công việc
      const updatedTodo = await this.todoRepository.update(tenantId, id, {
        ...updateTodoDto,
        updatedAt: Date.now(),
      });
      return updatedTodo ? await this.mapToResponseDto(updatedTodo) : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_UPDATE_FAILED,
        `Cập nhật công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật trạng thái công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param userId ID người dùng cập nhật
   * @param updateTodoStatusDto Thông tin trạng thái mới
   * @returns Công việc đã cập nhật
   */
  async updateTodoStatus(
    tenantId: number,
    id: number,
    userId: number,
    updateTodoStatusDto: UpdateTodoStatusDto,
  ): Promise<TodoResponseDto | null> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoEntityById(tenantId, id);

      // Kiểm tra quyền cập nhật trạng thái (chỉ người được giao hoặc người tạo mới có quyền cập nhật)
      if (todo.assigneeId !== userId && todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Bạn không có quyền cập nhật trạng thái công việc này',
        );
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData: any = {
        status: updateTodoStatusDto.status,
        updatedAt: Date.now(),
      };

      // Tự động set startedAt khi chuyển sang IN_PROGRESS (nếu chưa có)
      if (
        updateTodoStatusDto.status === TodoStatus.IN_PROGRESS &&
        !todo.startedAt
      ) {
        updateData.startedAt = Date.now();
      }

      // Tự động set completedAt khi chuyển sang COMPLETED
      if (updateTodoStatusDto.status === TodoStatus.COMPLETED) {
        updateData.completedAt = Date.now();
      }

      // Cập nhật công việc với dữ liệu đã chuẩn bị
      const updatedTodo = await this.todoRepository.update(tenantId, id, updateData);
      return updatedTodo ? await this.mapToResponseDto(updatedTodo) : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_STATUS_UPDATE_FAILED,
        `Cập nhật trạng thái công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param userId ID người dùng xóa
   * @returns true nếu xóa thành công
   */
  async deleteTodo(
    tenantId: number,
    id: number,
    userId: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoEntityById(tenantId, id);

      // Kiểm tra quyền xóa (chỉ người tạo mới có quyền xóa)
      if (todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Bạn không có quyền xóa công việc này',
        );
      }

      // Xóa công việc
      const result = await this.todoRepository.delete(tenantId, id);
      if (!result) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_DELETE_FAILED,
          `Xóa công việc thất bại`,
        );
      }
      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa công việc: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_DELETE_FAILED,
        `Xóa công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Chấm điểm cho công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param userId ID người dùng chấm điểm
   * @param scoreTodoDto Thông tin chấm điểm
   * @returns Công việc đã được chấm điểm
   */
  async scoreTodo(
    tenantId: number,
    id: number,
    userId: number,
    scoreTodoDto: ScoreTodoDto,
  ): Promise<TodoResponseDto | null> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoEntityById(tenantId, id);

      // Kiểm tra trạng thái công việc (chỉ chấm điểm khi công việc đã hoàn thành)
      if (todo.status !== TodoStatus.COMPLETED) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_COMPLETED,
          'Chỉ có thể chấm điểm cho công việc đã hoàn thành',
        );
      }

      // Kiểm tra quyền chấm điểm
      const canScore = await this.checkScoringPermission(
        tenantId,
        userId,
        todo,
      );
      if (!canScore) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_AUTHORIZED_TO_SCORE,
          'Bạn không có quyền chấm điểm cho công việc này',
        );
      }

      // Lưu thông tin chấm điểm
      const now = Date.now();
      await this.todoScoreRepository.create(tenantId, {
        todoId: id,
        scorerId: userId,
        awardedStars: scoreTodoDto.awardedStars,
        feedback: scoreTodoDto.feedback || null,
        createdAt: now,
      });

      // Cập nhật điểm cho công việc
      const updatedTodo = await this.todoRepository.updateScore(
        tenantId,
        id,
        scoreTodoDto.awardedStars,
      );
      return updatedTodo ? await this.mapToResponseDto(updatedTodo) : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi chấm điểm công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_SCORING_FAILED,
        `Chấm điểm công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra quyền chấm điểm
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng
   * @param todo Công việc cần chấm điểm
   * @returns true nếu có quyền chấm điểm
   */
  private async checkScoringPermission(
    tenantId: number,
    userId: number,
    todo: Todo,
  ): Promise<boolean> {
    // Trường hợp 1: Người tạo công việc (nếu khác với người được giao)
    if (todo.createdBy === userId && todo.assigneeId !== userId) {
      return true;
    }

    // Trường hợp 2: Admin của dự án (nếu công việc thuộc về dự án)
    if (todo.categoryId) {
      const projectMember =
        await this.projectMemberRepository.findByProjectIdAndUserId(
          tenantId,
          todo.categoryId,
          userId,
        );
      if (projectMember && projectMember.role === ProjectMemberRole.ADMIN) {
        return true;
      }
    }

    // TODO: Trường hợp 3: Cấp trên trực tiếp của người được giao nhiệm vụ
    // Cần tích hợp với module phòng ban để kiểm tra quan hệ cấp trên-cấp dưới

    return false;
  }

  /**
   * Lấy lịch sử chấm điểm của công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @returns Danh sách bản ghi chấm điểm
   */
  async getTodoScoreHistory(
    tenantId: number,
    todoId: number,
  ): Promise<TodoScore[]> {
    try {
      // Kiểm tra công việc tồn tại
      await this.findTodoEntityById(tenantId, todoId);

      // Lấy lịch sử chấm điểm
      return await this.todoScoreRepository.findByTodoId(tenantId, todoId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy lịch sử chấm điểm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_SCORE_HISTORY_FAILED,
        `Lấy lịch sử chấm điểm thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách công việc quá hạn cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param options Tùy chọn lọc
   * @returns Danh sách công việc quá hạn
   */
  async getOverdueTasks(
    tenantId: number,
    options: {
      assigneeId?: number;
      departmentId?: number;
      limit?: number;
    } = {},
  ): Promise<Todo[]> {
    try {
      // TODO: Cần thêm trường dueDate vào entity Todo để kiểm tra quá hạn
      // Hiện tại sử dụng logic tạm thời
      const query: TodoQueryDto = {
        page: 1,
        limit: options.limit || 10,
        assigneeId: options.assigneeId,
        status: TodoStatus.PENDING, // Chỉ lấy công việc chưa hoàn thành
      };

      const result = await this.findAllTodos(tenantId, query);

      // TODO: Lọc theo dueDate khi có trường này
      // Hiện tại trả về tất cả pending tasks
      return result.items;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách công việc quá hạn: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy danh sách công việc quá hạn thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách công việc của người dùng cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng
   * @param options Tùy chọn lọc
   * @returns Danh sách công việc
   */
  async getUserTasks(
    tenantId: number,
    userId: number,
    options: {
      status?: string;
      priority?: string;
      dueDate?: string;
      limit?: number;
    } = {},
  ): Promise<Todo[]> {
    try {
      const query: TodoQueryDto = {
        page: 1,
        limit: options.limit || 10,
        assigneeId: userId,
        status: options.status as TodoStatus,
        priority: options.priority as TodoPriority,
      };

      const result = await this.findAllTodos(tenantId, query);
      return result.items;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách công việc của người dùng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy danh sách công việc của người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết công việc cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param taskId ID công việc
   * @param options Tùy chọn
   * @returns Chi tiết công việc
   */
  async getTaskDetails(
    tenantId: number,
    taskId: number,
    options: {
      includeSubtasks?: boolean;
      includeComments?: boolean;
      includeAttachments?: boolean;
    } = {},
  ): Promise<Todo> {
    try {
      const todo = await this.findTodoEntityById(tenantId, taskId);

      // TODO: Tích hợp với các module khác để lấy thêm thông tin
      // - Subtasks: sử dụng findSubtasks
      // - Comments: cần tích hợp với module comments
      // - Attachments: cần tích hợp với module attachments

      if (options.includeSubtasks) {
        // Có thể mở rộng để include subtasks
        this.logger.debug(`Including subtasks for todo ${taskId}`);
      }

      return todo;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy chi tiết công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy chi tiết công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng xóa
   * @param dto DTO chứa danh sách ID công việc cần xóa
   * @returns Kết quả xóa nhiều công việc
   */
  async bulkDeleteTodos(
    tenantId: number,
    userId: number,
    dto: BulkDeleteTodoDto,
  ): Promise<BulkDeleteTodoResponseDto> {
    try {
      const { ids } = dto;

      // Tìm tất cả công việc theo danh sách ID
      const todos = await this.todoRepository.findByIds(tenantId, ids);
      const foundIds = todos.map((todo) => todo.id);
      const notFoundIds = ids.filter((id) => !foundIds.includes(id));

      const result: BulkDeleteTodoResponseDto = {
        totalRequested: ids.length,
        successCount: 0,
        failureCount: 0,
        deletedIds: [],
        failures: [],
      };

      // Thêm các ID không tìm thấy vào danh sách lỗi
      notFoundIds.forEach((id) => {
        result.failures.push({
          id,
          reason: 'Không tìm thấy công việc',
        });
        result.failureCount++;
      });

      // Kiểm tra quyền xóa và xóa từng công việc
      for (const todo of todos) {
        try {
          // Kiểm tra quyền xóa (chỉ người tạo mới có quyền xóa)
          if (todo.createdBy !== userId) {
            result.failures.push({
              id: todo.id,
              reason: 'Bạn không có quyền xóa công việc này',
            });
            result.failureCount++;
            continue;
          }

          // Xóa công việc
          const deleted = await this.todoRepository.delete(tenantId, todo.id);
          if (deleted) {
            result.deletedIds.push(todo.id);
            result.successCount++;
          } else {
            result.failures.push({
              id: todo.id,
              reason: 'Xóa công việc thất bại',
            });
            result.failureCount++;
          }
        } catch (error) {
          result.failures.push({
            id: todo.id,
            reason: `Lỗi khi xóa: ${error.message}`,
          });
          result.failureCount++;
        }
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa nhiều công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_DELETE_FAILED,
        `Xóa nhiều công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật nhiều công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng cập nhật
   * @param dto DTO chứa danh sách ID công việc và dữ liệu cập nhật
   * @returns Kết quả cập nhật nhiều công việc
   */
  async bulkUpdateTodos(
    tenantId: number,
    userId: number,
    dto: BulkUpdateTodoDto,
  ): Promise<BulkUpdateTodoResponseDto> {
    try {
      const { ids, ...updateData } = dto;

      // Tìm tất cả công việc theo danh sách ID
      const todos = await this.todoRepository.findByIds(tenantId, ids);
      const foundIds = todos.map((todo) => todo.id);
      const notFoundIds = ids.filter((id) => !foundIds.includes(id));

      const result: BulkUpdateTodoResponseDto = {
        totalRequested: ids.length,
        successCount: 0,
        failureCount: 0,
        updatedIds: [],
        failures: [],
      };

      // Thêm lỗi cho các ID không tìm thấy
      notFoundIds.forEach((id) => {
        result.failures.push({
          id,
          reason: 'Không tìm thấy công việc',
        });
        result.failureCount++;
      });

      // Cập nhật từng công việc
      for (const todo of todos) {
        try {
          // Kiểm tra quyền cập nhật (người tạo, người được giao, hoặc admin)
          if (todo.createdBy !== userId && todo.assigneeId !== userId) {
            result.failures.push({
              id: todo.id,
              reason: 'Bạn không có quyền cập nhật công việc này',
            });
            result.failureCount++;
            continue;
          }

          // Chuẩn bị dữ liệu cập nhật
          const updatePayload: any = {
            ...updateData,
            updatedAt: Date.now(),
          };

          // Cập nhật công việc
          await this.todoRepository.update(tenantId, todo.id, updatePayload);

          result.updatedIds.push(todo.id);
          result.successCount++;

          this.logger.log(`Đã cập nhật công việc ${todo.id} bởi user ${userId}`);
        } catch (error) {
          result.failures.push({
            id: todo.id,
            reason: `Lỗi cập nhật: ${error.message}`,
          });
          result.failureCount++;
          this.logger.error(
            `Lỗi khi cập nhật công việc ${todo.id}: ${error.message}`,
            error.stack,
          );
        }
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật nhiều công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_BULK_UPDATE_FAILED,
        `Cập nhật nhiều công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật người được giao việc
   * @param tenantId ID tenant
   * @param id ID công việc
   * @param userId ID người dùng thực hiện cập nhật
   * @param updateTodoAssigneeDto Thông tin người được giao việc mới
   * @returns Công việc đã cập nhật assignee
   */
  async updateTodoAssignee(
    tenantId: number,
    id: number,
    userId: number,
    updateTodoAssigneeDto: { assigneeId?: number | null },
  ): Promise<TodoResponseDto | null | undefined> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoEntityById(tenantId, id);

      // Kiểm tra quyền cập nhật assignee (chỉ người tạo hoặc admin có quyền)
      // Người được giao hiện tại không thể tự giao lại cho người khác
      if (todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Chỉ người tạo công việc mới có quyền thay đổi người được giao việc',
        );
      }

      // Kiểm tra assigneeId mới có tồn tại không (nếu không null)
      if (updateTodoAssigneeDto.assigneeId) {
        // TODO: Có thể thêm validation kiểm tra employee tồn tại
        // const employee = await this.employeeRepository.findById(tenantId, updateTodoAssigneeDto.assigneeId);
        // if (!employee) {
        //   throw new AppException(TODOLISTS_ERROR_CODES.EMPLOYEE_NOT_FOUND, 'Không tìm thấy nhân viên');
        // }
      }

      // Cập nhật assignee
      if (updateTodoAssigneeDto.assigneeId) {
        const updatedTodo = await this.todoRepository.update(tenantId, id, {
          assigneeId: updateTodoAssigneeDto.assigneeId,
          updatedAt: Date.now(),
        });
        return updatedTodo ? await this.mapToResponseDto(updatedTodo) : null;
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật assignee công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_UPDATE_FAILED,
        `Cập nhật assignee công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Thiết lập deadline cho công việc
   * @param tenantId ID tenant
   * @param id ID công việc
   * @param userId ID người dùng thiết lập deadline
   * @param setDeadlineDto Thông tin deadline
   * @returns Công việc đã cập nhật deadline
   */
  async setDeadline(
    tenantId: number,
    id: number,
    userId: number,
    setDeadlineDto: SetDeadlineDto,
  ): Promise<TodoResponseDto | null> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoEntityById(tenantId, id);

      // Kiểm tra quyền thiết lập deadline (người tạo hoặc người được giao)
      if (todo.assigneeId !== userId && todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Bạn không có quyền thiết lập deadline cho công việc này',
        );
      }

      // Cập nhật deadline
      const updatedTodo = await this.todoRepository.update(tenantId, id, {
        deadline: setDeadlineDto.deadline,
        updatedAt: Date.now(),
      });

      return updatedTodo ? await this.mapToResponseDto(updatedTodo) : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi thiết lập deadline: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_UPDATE_FAILED,
        `Thiết lập deadline thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thống kê deadline
   * @param tenantId ID tenant
   * @param query Tham số truy vấn
   * @returns Thống kê deadline
   */
  async getDeadlineStatistics(
    tenantId: number,
    query: DeadlineStatisticsQueryDto,
  ): Promise<DeadlineStatisticsResponseDto> {
    try {
      const now = Date.now();
      const warningDays = query.warningDays || 3;
      const warningThreshold = now + warningDays * 24 * 60 * 60 * 1000;

      // Lấy danh sách công việc có deadline
      const todos = await this.todoRepository.findTodosWithDeadline(
        tenantId,
        query,
      );

      // Phân loại công việc
      const overdue: DeadlineTodoItemDto[] = [];
      const nearDeadline: DeadlineTodoItemDto[] = [];
      const onTime: DeadlineTodoItemDto[] = [];

      // Thống kê theo dự án và người được giao
      const byProject: Record<
        string,
        { overdue: number; nearDeadline: number; onTime: number }
      > = {};
      const byAssignee: Record<
        string,
        { overdue: number; nearDeadline: number; onTime: number }
      > = {};

      for (const todo of todos) {
        if (!todo.deadline) continue;

        const daysRemaining = Math.ceil(
          (todo.deadline - now) / (24 * 60 * 60 * 1000),
        );

        const todoItem: DeadlineTodoItemDto = {
          id: todo.id,
          title: todo.title,
          description: todo.description,
          assigneeId: todo.assigneeId,
          assigneeName: todo.assigneeName || null,
          projectId: todo.categoryId,
          projectName: todo.projectName || null,
          deadline: todo.deadline,
          daysRemaining,
          status: todo.status || 'pending',
          priority: todo.priority,
          tags: todo.tags || null,
        };

        // Phân loại theo deadline
        if (todo.deadline < now) {
          overdue.push(todoItem);
        } else if (todo.deadline <= warningThreshold) {
          nearDeadline.push(todoItem);
        } else {
          onTime.push(todoItem);
        }

        // Thống kê theo dự án
        const projectName = todo.projectName || 'Không có dự án';
        if (!byProject[projectName]) {
          byProject[projectName] = { overdue: 0, nearDeadline: 0, onTime: 0 };
        }
        if (todo.deadline < now) {
          byProject[projectName].overdue++;
        } else if (todo.deadline <= warningThreshold) {
          byProject[projectName].nearDeadline++;
        } else {
          byProject[projectName].onTime++;
        }

        // Thống kê theo người được giao
        const assigneeName = todo.assigneeName || 'Chưa giao';
        if (!byAssignee[assigneeName]) {
          byAssignee[assigneeName] = { overdue: 0, nearDeadline: 0, onTime: 0 };
        }
        if (todo.deadline < now) {
          byAssignee[assigneeName].overdue++;
        } else if (todo.deadline <= warningThreshold) {
          byAssignee[assigneeName].nearDeadline++;
        } else {
          byAssignee[assigneeName].onTime++;
        }
      }

      return {
        totalWithDeadline: todos.length,
        overdue: overdue.length,
        nearDeadline: nearDeadline.length,
        onTime: onTime.length,
        overdueItems: overdue,
        nearDeadlineItems: nearDeadline,
        byProject,
        byAssignee,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy thống kê deadline: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy thống kê deadline thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thời gian bắt đầu làm việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param userId ID người dùng cập nhật
   * @param updateTodoStartedAtDto Thông tin thời gian bắt đầu
   * @returns Công việc đã cập nhật
   */
  async updateTodoStartedAt(
    tenantId: number,
    id: number,
    userId: number,
    updateTodoStartedAtDto: { startedAt?: number | null } = {},
  ): Promise<TodoResponseDto | null> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoEntityById(tenantId, id);

      // Kiểm tra quyền cập nhật (chỉ người được giao hoặc người tạo mới có quyền cập nhật)
      if (todo.assigneeId !== userId && todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Bạn không có quyền cập nhật thời gian bắt đầu công việc này',
        );
      }

      // Xác định thời gian bắt đầu
      const startedAt = updateTodoStartedAtDto.startedAt ?? Date.now();

      // Cập nhật thời gian bắt đầu
      const updatedTodo = await this.todoRepository.update(tenantId, id, {
        startedAt,
        updatedAt: Date.now(),
      });

      return updatedTodo ? await this.mapToResponseDto(updatedTodo) : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật thời gian bắt đầu công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_UPDATE_FAILED,
        `Cập nhật thời gian bắt đầu công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Tìm kiếm nâng cao công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số tìm kiếm nâng cao
   * @returns Danh sách công việc đã phân trang
   */
  async advancedSearchTodos(
    tenantId: number,
    query: AdvancedSearchTodoDto,
  ): Promise<PaginatedResult<TodoResponseDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        keyword,
        statuses,
        priorities,
        assigneeIds,
        categoryIds,
        tagIds,
        createdFrom,
        createdTo,
        deadlineFrom,
        deadlineTo,
        minExpectedStars,
        maxExpectedStars,
        hasDeadline,
        isOverdue,
        hasSubtasks,
      } = query;

      const queryBuilder = this.todoRepository.createQueryBuilder('todo');

      // Add tenantId filtering - REQUIRED for tenant isolation
      queryBuilder.where('todo.tenantId = :tenantId', { tenantId });

      // Tìm kiếm theo từ khóa trong tiêu đề và mô tả
      if (keyword) {
        queryBuilder.andWhere(
          '(todo.title LIKE :keyword OR todo.description LIKE :keyword)',
          { keyword: `%${keyword}%` },
        );
      }

      // Lọc theo trạng thái
      if (statuses && statuses.length > 0) {
        queryBuilder.andWhere('todo.status IN (:...statuses)', { statuses });
      }

      // Lọc theo mức độ ưu tiên
      if (priorities && priorities.length > 0) {
        queryBuilder.andWhere('todo.priority IN (:...priorities)', { priorities });
      }

      // Lọc theo người được giao
      if (assigneeIds && assigneeIds.length > 0) {
        queryBuilder.andWhere('todo.assigneeId IN (:...assigneeIds)', { assigneeIds });
      }

      // Lọc theo dự án
      if (categoryIds && categoryIds.length > 0) {
        queryBuilder.andWhere('todo.categoryId IN (:...categoryIds)', { categoryIds });
      }

      // Lọc theo tags
      if (tagIds && tagIds.length > 0) {
        queryBuilder
          .leftJoin('todo_tag', 'tt', 'tt.todoId = todo.id AND tt.tenantId = :tenantId')
          .andWhere('tt.tagId IN (:...tagIds)', { tagIds });
      }

      // Lọc theo ngày tạo
      if (createdFrom) {
        const fromTimestamp = new Date(createdFrom).getTime();
        queryBuilder.andWhere('todo.createdAt >= :createdFrom', { createdFrom: fromTimestamp });
      }

      if (createdTo) {
        const toTimestamp = new Date(createdTo).getTime();
        queryBuilder.andWhere('todo.createdAt <= :createdTo', { createdTo: toTimestamp });
      }

      // Lọc theo deadline
      if (deadlineFrom) {
        const fromTimestamp = new Date(deadlineFrom).getTime();
        queryBuilder.andWhere('todo.deadline >= :deadlineFrom', { deadlineFrom: fromTimestamp });
      }

      if (deadlineTo) {
        const toTimestamp = new Date(deadlineTo).getTime();
        queryBuilder.andWhere('todo.deadline <= :deadlineTo', { deadlineTo: toTimestamp });
      }

      // Lọc theo số sao kỳ vọng
      if (minExpectedStars) {
        queryBuilder.andWhere('todo.expectedStars >= :minExpectedStars', { minExpectedStars });
      }

      if (maxExpectedStars) {
        queryBuilder.andWhere('todo.expectedStars <= :maxExpectedStars', { maxExpectedStars });
      }

      // Lọc công việc có deadline
      if (hasDeadline !== undefined) {
        if (hasDeadline) {
          queryBuilder.andWhere('todo.deadline IS NOT NULL');
        } else {
          queryBuilder.andWhere('todo.deadline IS NULL');
        }
      }

      // Lọc công việc quá hạn
      if (isOverdue !== undefined && isOverdue) {
        const now = Date.now();
        queryBuilder.andWhere('todo.deadline IS NOT NULL AND todo.deadline < :now', { now });
      }

      // Lọc công việc có subtasks
      if (hasSubtasks !== undefined) {
        if (hasSubtasks) {
          queryBuilder
            .leftJoin('todo', 'subtask', 'subtask.parentId = todo.id AND subtask.tenantId = :tenantId')
            .andWhere('subtask.id IS NOT NULL');
        } else {
          queryBuilder
            .leftJoin('todo', 'subtask', 'subtask.parentId = todo.id AND subtask.tenantId = :tenantId')
            .andWhere('subtask.id IS NULL');
        }
      }

      // Áp dụng sắp xếp
      queryBuilder.orderBy(`todo.${sortBy}`, sortDirection);

      // Áp dụng phân trang
      const [items, totalItems] = await queryBuilder
        .skip((page - 1) * limit)
        .take(limit)
        .getManyAndCount();

      const todoResponseDtos = items.map((item) => this.mapToResponseDto(item));

      return {
        items: todoResponseDtos,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm kiếm nâng cao công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_SEARCH_FAILED,
        `Tìm kiếm nâng cao công việc thất bại: ${error.message}`,
      );
    }
  }
}
