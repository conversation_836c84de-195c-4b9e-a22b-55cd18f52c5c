import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsEnum, IsArray, IsNumber, IsDateString, MaxLength, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tạo chiến dịch mới
 */
export class CreateCampaignDto {
  /**
   * Tên chiến dịch
   */
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch Marketing Q4 2024',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên chiến dịch không được để trống' })
  @IsString({ message: 'Tên chiến dịch phải là chuỗi' })
  @MaxLength(255, { message: 'Tên chiến dịch không được vượt quá 255 ký tự' })
  title: string;

  /**
   * <PERSON><PERSON> tả chiến dịch
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả chiến dịch',
    example: '<PERSON>ế<PERSON> dịch marketing tổng thể cho quý 4 năm 2024',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * ID người sở hữu chiến dịch
   */
  @ApiProperty({
    description: 'ID người sở hữu chiến dịch',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'ID người sở hữu phải là số' })
  @Min(1, { message: 'ID người sở hữu phải lớn hơn 0' })
  ownerId?: number;

  /**
   * Ngày bắt đầu chiến dịch
   */
  @ApiProperty({
    description: 'Ngày bắt đầu chiến dịch (ISO string)',
    example: '2024-10-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  /**
   * Ngày kết thúc chiến dịch
   */
  @ApiProperty({
    description: 'Ngày kết thúc chiến dịch (ISO string)',
    example: '2024-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  /**
   * Mức độ ưu tiên
   */
  @ApiProperty({
    description: 'Mức độ ưu tiên',
    enum: ['low', 'medium', 'high', 'urgent'],
    example: 'high',
    required: false,
    default: 'medium',
  })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high', 'urgent'], { message: 'Mức độ ưu tiên không hợp lệ' })
  priority?: 'low' | 'medium' | 'high' | 'urgent';

  /**
   * Ngân sách chiến dịch
   */
  @ApiProperty({
    description: 'Ngân sách chiến dịch',
    example: 1000000000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Ngân sách phải là số' })
  @Min(0, { message: 'Ngân sách phải lớn hơn hoặc bằng 0' })
  budget?: number;

  /**
   * Đơn vị tiền tệ
   */
  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    required: false,
    default: 'VND',
  })
  @IsOptional()
  @IsString({ message: 'Đơn vị tiền tệ phải là chuỗi' })
  @MaxLength(10, { message: 'Đơn vị tiền tệ không được vượt quá 10 ký tự' })
  currency?: string;

  /**
   * Mục tiêu chiến dịch
   */
  @ApiProperty({
    description: 'Mục tiêu chiến dịch',
    example: 'Tăng nhận diện thương hiệu và doanh số bán hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mục tiêu phải là chuỗi' })
  objectives?: string;

  /**
   * Nhóm mục tiêu
   */
  @ApiProperty({
    description: 'Nhóm mục tiêu',
    example: 'Khách hàng từ 25-40 tuổi, thu nhập trung bình khá',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nhóm mục tiêu phải là chuỗi' })
  targetAudience?: string;

  /**
   * Kênh truyền thông
   */
  @ApiProperty({
    description: 'Kênh truyền thông',
    example: ['facebook', 'google-ads', 'email', 'website'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Kênh truyền thông phải là mảng' })
  @IsString({ each: true, message: 'Mỗi kênh truyền thông phải là chuỗi' })
  channels?: string[];

  /**
   * Tags/nhãn
   */
  @ApiProperty({
    description: 'Tags/nhãn',
    example: ['marketing', 'q4-2024', 'brand-awareness'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[];
}
