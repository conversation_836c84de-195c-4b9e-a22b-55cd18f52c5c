import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { SshServerService } from '../services/ssh-server.service';
import { CreateSshServerDto } from '../dtos/requests/create-ssh-server.dto';
import { UpdateSshServerDto } from '../dtos/requests/update-ssh-server.dto';
import {
  SshServerResponseDto,
  SshServerListResponseDto,
  SshConnectionTestResponseDto,
  SshServerStatisticsResponseDto,
} from '../dtos/responses/ssh-server.dto';
import { ApiResponseDto } from '@/common/response';
import { JwtPayload } from '@/modules/auth/guards';

/**
 * Controller quản lý SSH servers
 */
@ApiTags('SSH Server Management')
@Controller('ssh-servers')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class SshServerController {
  constructor(private readonly sshServerService: SshServerService) {}

  /**
   * Tạo SSH server mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo SSH server mới',
    description: 'Tạo SSH server mới với thông tin kết nối được mã hóa',
  })
  @ApiResponse({
    status: 201,
    description: 'SSH server đã được tạo thành công',
    type: ApiResponseDto<SshServerResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 409,
    description: 'Tên SSH server đã tồn tại',
  })
  async create(
    @Body() createDto: CreateSshServerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<SshServerResponseDto>> {
    const server = await this.sshServerService.create(createDto, user.sub);
    
    return {
      success: true,
      message: 'SSH server đã được tạo thành công',
      data: this.mapToResponseDto(server),
    };
  }

  /**
   * Lấy danh sách SSH servers với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách SSH servers',
    description: 'Lấy danh sách SSH servers với phân trang và tìm kiếm',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (mặc định: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Số items per page (mặc định: 10)' })
  @ApiQuery({ name: 'search', required: false, description: 'Từ khóa tìm kiếm' })
  @ApiQuery({ name: 'isActive', required: false, description: 'Lọc theo trạng thái hoạt động' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách SSH servers',
    type: ApiResponseDto<SshServerListResponseDto>,
  })
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('isActive') isActive?: boolean,
  ): Promise<ApiResponseDto<SshServerListResponseDto>> {
    const pageNum = page || 1;
    const limitNum = limit || 10;
    
    const { items, total } = await this.sshServerService.findWithPagination({
      page: pageNum,
      limit: limitNum,
      search,
      isActive,
    });

    const responseData: SshServerListResponseDto = {
      items: items.map(server => this.mapToResponseDto(server)),
      total,
      page: pageNum,
      limit: limitNum,
      totalPages: Math.ceil(total / limitNum),
    };

    return {
      success: true,
      message: 'Lấy danh sách SSH servers thành công',
      data: responseData,
    };
  }

  /**
   * Lấy SSH server theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy SSH server theo ID',
    description: 'Lấy thông tin chi tiết SSH server theo ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin SSH server',
    type: ApiResponseDto<SshServerResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'SSH server không tồn tại',
  })
  async findById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<SshServerResponseDto>> {
    const server = await this.sshServerService.findByIdOrThrow(id);
    
    return {
      success: true,
      message: 'Lấy thông tin SSH server thành công',
      data: this.mapToResponseDto(server),
    };
  }

  /**
   * Cập nhật SSH server
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật SSH server',
    description: 'Cập nhật thông tin SSH server',
  })
  @ApiResponse({
    status: 200,
    description: 'SSH server đã được cập nhật thành công',
    type: ApiResponseDto<SshServerResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'SSH server không tồn tại',
  })
  @ApiResponse({
    status: 409,
    description: 'Tên SSH server đã tồn tại',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateSshServerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<SshServerResponseDto>> {
    const server = await this.sshServerService.update(id, updateDto, user.sub);
    
    return {
      success: true,
      message: 'SSH server đã được cập nhật thành công',
      data: this.mapToResponseDto(server),
    };
  }

  /**
   * Xóa SSH server
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa SSH server',
    description: 'Xóa SSH server (soft delete)',
  })
  @ApiResponse({
    status: 200,
    description: 'SSH server đã được xóa thành công',
  })
  @ApiResponse({
    status: 404,
    description: 'SSH server không tồn tại',
  })
  async delete(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<null>> {
    await this.sshServerService.softDelete(id);
    
    return {
      success: true,
      message: 'SSH server đã được xóa thành công',
      data: null,
    };
  }

  /**
   * Test kết nối SSH
   */
  @Post(':id/test-connection')
  @ApiOperation({
    summary: 'Test kết nối SSH',
    description: 'Kiểm tra kết nối SSH tới server',
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả test kết nối',
    type: ApiResponseDto<SshConnectionTestResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'SSH server không tồn tại',
  })
  async testConnection(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<SshConnectionTestResponseDto>> {
    const result = await this.sshServerService.testConnection(id);
    
    return {
      success: result.success,
      message: result.message,
      data: result,
    };
  }

  /**
   * Kích hoạt SSH server
   */
  @Post(':id/activate')
  @ApiOperation({
    summary: 'Kích hoạt SSH server',
    description: 'Kích hoạt SSH server đã bị vô hiệu hóa',
  })
  @ApiResponse({
    status: 200,
    description: 'SSH server đã được kích hoạt',
  })
  @ApiResponse({
    status: 404,
    description: 'SSH server không tồn tại',
  })
  async activate(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.sshServerService.activate(id, user.sub);
    
    return {
      success: true,
      message: 'SSH server đã được kích hoạt',
      data: null,
    };
  }

  /**
   * Vô hiệu hóa SSH server
   */
  @Post(':id/deactivate')
  @ApiOperation({
    summary: 'Vô hiệu hóa SSH server',
    description: 'Vô hiệu hóa SSH server',
  })
  @ApiResponse({
    status: 200,
    description: 'SSH server đã được vô hiệu hóa',
  })
  @ApiResponse({
    status: 404,
    description: 'SSH server không tồn tại',
  })
  async deactivate(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.sshServerService.deactivate(id, user.sub);
    
    return {
      success: true,
      message: 'SSH server đã được vô hiệu hóa',
      data: null,
    };
  }

  /**
   * Lấy thống kê SSH servers
   */
  @Get('statistics/overview')
  @ApiOperation({
    summary: 'Lấy thống kê SSH servers',
    description: 'Lấy thống kê tổng quan về SSH servers',
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê SSH servers',
    type: ApiResponseDto<SshServerStatisticsResponseDto>,
  })
  async getStatistics(): Promise<ApiResponseDto<SshServerStatisticsResponseDto>> {
    const statistics = await this.sshServerService.getStatistics();

    return {
      success: true,
      message: 'Lấy thống kê SSH servers thành công',
      data: statistics,
    };
  }

  /**
   * Map SSH server entity to response DTO
   */
  private mapToResponseDto(server: any): SshServerResponseDto {
    return {
      id: server.id,
      name: server.name,
      host: server.host,
      port: server.port,
      username: server.username,
      description: server.description,
      isActive: server.isActive,
      connectionTimeout: server.connectionTimeout,
      commandTimeout: server.commandTimeout,
      retryAttempts: server.retryAttempts,
      retryDelay: server.retryDelay,
      lastConnectedAt: server.lastConnectedAt,
      lastConnectionStatus: server.lastConnectionStatus,
      lastConnectionError: server.lastConnectionError,
      createdBy: server.createdBy,
      updatedBy: server.updatedBy,
      createdAt: server.createdAt,
      updatedAt: server.updatedAt,
    };
  }
}
