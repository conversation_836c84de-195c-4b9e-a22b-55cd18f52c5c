import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsArray, IsNumber, IsDateString, MaxLength, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho cập nhật chiến dịch
 */
export class UpdateCampaignDto {
  /**
   * Tên chiến dịch
   */
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch Marketing Q4 2024 - Updated',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên chiến dịch phải là chuỗi' })
  @MaxLength(255, { message: 'Tên chiến dịch không được vượt quá 255 ký tự' })
  title?: string;

  /**
   * <PERSON>ô tả chiến dịch
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả chiến dịch',
    example: 'Chiến dịch marketing tổng thể cho quý 4 năm 2024 - <PERSON><PERSON>n bản cập nhật',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> tả phải là chuỗi' })
  description?: string;

  /**
   * Ngày bắt đầu chiến dịch
   */
  @ApiProperty({
    description: 'Ngày bắt đầu chiến dịch (ISO string)',
    example: '2024-10-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  /**
   * Ngày kết thúc chiến dịch
   */
  @ApiProperty({
    description: 'Ngày kết thúc chiến dịch (ISO string)',
    example: '2024-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  /**
   * Trạng thái chiến dịch
   */
  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ['draft', 'active', 'paused', 'completed', 'cancelled'],
    example: 'active',
    required: false,
  })
  @IsOptional()
  @IsEnum(['draft', 'active', 'paused', 'completed', 'cancelled'], { message: 'Trạng thái không hợp lệ' })
  status?: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';

  /**
   * Mức độ ưu tiên
   */
  @ApiProperty({
    description: 'Mức độ ưu tiên',
    enum: ['low', 'medium', 'high', 'urgent'],
    example: 'high',
    required: false,
  })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high', 'urgent'], { message: 'Mức độ ưu tiên không hợp lệ' })
  priority?: 'low' | 'medium' | 'high' | 'urgent';

  /**
   * Ngân sách chiến dịch
   */
  @ApiProperty({
    description: 'Ngân sách chiến dịch',
    example: 1500000000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Ngân sách phải là số' })
  @Min(0, { message: 'Ngân sách phải lớn hơn hoặc bằng 0' })
  budget?: number;

  /**
   * Đơn vị tiền tệ
   */
  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Đơn vị tiền tệ phải là chuỗi' })
  @MaxLength(10, { message: 'Đơn vị tiền tệ không được vượt quá 10 ký tự' })
  currency?: string;

  /**
   * Mục tiêu chiến dịch
   */
  @ApiProperty({
    description: 'Mục tiêu chiến dịch',
    example: 'Tăng nhận diện thương hiệu và doanh số bán hàng 25%',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mục tiêu phải là chuỗi' })
  objectives?: string;

  /**
   * Nhóm mục tiêu
   */
  @ApiProperty({
    description: 'Nhóm mục tiêu',
    example: 'Khách hàng từ 25-40 tuổi, thu nhập trung bình khá, quan tâm công nghệ',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nhóm mục tiêu phải là chuỗi' })
  targetAudience?: string;

  /**
   * Kênh truyền thông
   */
  @ApiProperty({
    description: 'Kênh truyền thông',
    example: ['facebook', 'google-ads', 'email', 'website', 'tiktok'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Kênh truyền thông phải là mảng' })
  @IsString({ each: true, message: 'Mỗi kênh truyền thông phải là chuỗi' })
  channels?: string[];

  /**
   * Tags/nhãn
   */
  @ApiProperty({
    description: 'Tags/nhãn',
    example: ['marketing', 'q4-2024', 'brand-awareness', 'digital'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[];
}
