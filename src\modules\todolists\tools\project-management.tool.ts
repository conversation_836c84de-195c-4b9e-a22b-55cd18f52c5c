import { Injectable } from '@nestjs/common';
import { ProjectService } from '../services/project.service';
import { CreateProjectDto } from '../dto/project/create-project.dto';
import { UpdateProjectDto } from '../dto/project/update-project.dto';
import { ProjectQueryDto } from '../dto/project/project-query.dto';
import { CreateProjectMemberDto } from '../dto/project-member/create-project-member.dto';

/**
 * Tool cho AI agent quản lý projects
 */
@Injectable()
export class ProjectManagementTool {
  constructor(private readonly projectService: ProjectService) {}

  /**
   * Tạo project mới
   */
  async createProject(params: {
    tenantId: number;
    userId: number;
    title: string;
    description?: string;
    ownerId?: number;
  }) {
    const createDto: CreateProjectDto = {
      title: params.title,
      description: params.description,
      ownerId: params.ownerId,
    };

    return this.projectService.createProject(params.tenantId, params.userId, createDto);
  }

  /**
   * Cập nhật project
   */
  async updateProject(params: {
    tenantId: number;
    projectId: number;
    userId: number;
    title?: string;
    description?: string;
    isActive?: boolean;
  }) {
    const updateDto: UpdateProjectDto = {
      title: params.title,
      description: params.description,
      isActive: params.isActive,
    };

    return this.projectService.updateProject(params.tenantId, params.projectId, params.userId, updateDto);
  }

  /**
   * Lấy danh sách projects
   */
  async getProjects(params: {
    tenantId: number;
    page?: number;
    limit?: number;
    keyword?: string;
    ownerId?: number;
    isActive?: boolean;
  }) {
    const query: ProjectQueryDto = {
      page: params.page || 1,
      limit: params.limit || 10,
      keyword: params.keyword,
      ownerId: params.ownerId,
      isActive: params.isActive,
    };

    return this.projectService.findAllProjects(params.tenantId, query);
  }

  /**
   * Lấy project theo ID
   */
  async getProjectById(params: {
    tenantId: number;
    projectId: number;
  }) {
    return this.projectService.findProjectById(params.tenantId, params.projectId);
  }

  /**
   * Xóa project
   */
  async deleteProject(params: {
    tenantId: number;
    projectId: number;
    userId: number;
  }) {
    return this.projectService.deleteProject(params.tenantId, params.projectId, params.userId);
  }

  /**
   * Thêm thành viên vào project
   */
  async addProjectMember(params: {
    tenantId: number;
    projectId: number;
    userId: number;
    memberId: number;
    role: 'admin' | 'member' | 'viewer';
  }) {
    const createMemberDto: CreateProjectMemberDto = {
      userId: params.memberId,
      role: params.role,
    };

    return this.projectService.addProjectMember(
      params.tenantId,
      params.projectId,
      params.userId,
      createMemberDto
    );
  }

  /**
   * Lấy danh sách thành viên project
   */
  async getProjectMembers(params: {
    tenantId: number;
    projectId: number;
    userId: number;
    page?: number;
    limit?: number;
  }) {
    const query = {
      page: params.page || 1,
      limit: params.limit || 10,
    };

    return this.projectService.findProjectMembers(
      params.tenantId,
      params.projectId,
      params.userId,
      query
    );
  }

  /**
   * Xóa thành viên khỏi project
   */
  async removeProjectMember(params: {
    tenantId: number;
    projectId: number;
    memberId: number;
    userId: number;
  }) {
    return this.projectService.removeProjectMember(
      params.tenantId,
      params.projectId,
      params.memberId,
      params.userId
    );
  }

  /**
   * Lấy analytics của project
   */
  async getProjectAnalytics(params: {
    tenantId: number;
    projectId: number;
    userId: number;
    startDate?: string;
    endDate?: string;
    memberIds?: number[];
    includeTimeline?: boolean;
    includeMemberStats?: boolean;
  }) {
    const query = {
      startDate: params.startDate,
      endDate: params.endDate,
      memberIds: params.memberIds,
      includeTimeline: params.includeTimeline,
      includeMemberStats: params.includeMemberStats,
    };

    return this.projectService.getProjectAnalytics(
      params.tenantId,
      params.projectId,
      params.userId,
      query
    );
  }

  /**
   * Lấy todos của project
   */
  async getProjectTodos(params: {
    tenantId: number;
    projectId: number;
    page?: number;
    limit?: number;
    status?: string;
    assigneeId?: number;
  }) {
    // TODO: Implement method trong ProjectService để lấy todos của project
    return {
      items: [],
      meta: {
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: params.limit || 10,
        totalPages: 0,
        currentPage: params.page || 1,
      },
    };
  }

  /**
   * Lấy thống kê tổng quan của user về projects
   */
  async getUserProjectStats(params: {
    tenantId: number;
    userId: number;
  }) {
    // TODO: Implement method trong ProjectService để lấy stats của user
    return {
      totalProjects: 0,
      ownedProjects: 0,
      memberProjects: 0,
      activeProjects: 0,
      completedProjects: 0,
    };
  }
}
