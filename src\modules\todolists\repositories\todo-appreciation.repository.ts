import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TodoAppreciation } from '../entities/todo-appreciation.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { TodoAppreciationQueryDto } from '../dto/todo-appreciation/todo-appreciation-query.dto';

/**
 * Repository cho entity TodoAppreciation
 */
@Injectable()
export class TodoAppreciationRepository {
  constructor(
    @InjectRepository(TodoAppreciation)
    private readonly repository: Repository<TodoAppreciation>,
  ) {}

  /**
   * Tạo appreciation mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu appreciation
   * @returns Appreciation đã tạo
   */
  async create(
    tenantId: number,
    data: Partial<TodoAppreciation>,
  ): Promise<TodoAppreciation> {
    const appreciation = this.repository.create({
      ...data,
      tenantId,
      createdAt: Date.now(),
    });
    return this.repository.save(appreciation);
  }

  /**
   * Tìm tất cả appreciation với phân trang
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách appreciation đã phân trang
   */
  async findAll(
    tenantId: number,
    query: TodoAppreciationQueryDto,
  ): Promise<PaginatedResult<TodoAppreciation>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      todoId,
      userId,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('appreciation');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.where('appreciation.tenantId = :tenantId', { tenantId });

    // Lọc theo todoId nếu có
    if (todoId) {
      queryBuilder.andWhere('appreciation.todoId = :todoId', { todoId });
    }

    // Lọc theo userId nếu có
    if (userId) {
      queryBuilder.andWhere('appreciation.userId = :userId', { userId });
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`appreciation.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm appreciation theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID appreciation
   * @returns Appreciation hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<TodoAppreciation | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm appreciation theo todoId
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID todo
   * @returns Danh sách appreciation
   */
  async findByTodoId(tenantId: number, todoId: number): Promise<TodoAppreciation[]> {
    return this.repository.find({
      where: { todoId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Cập nhật appreciation
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID appreciation
   * @param data Dữ liệu cập nhật
   * @returns Appreciation đã cập nhật
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<TodoAppreciation>,
  ): Promise<TodoAppreciation | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Xóa appreciation
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID appreciation
   * @returns Kết quả xóa
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Kiểm tra xem user có quyền cập nhật appreciation không
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID appreciation
   * @param userId ID user
   * @returns True nếu có quyền
   */
  async canUserUpdate(tenantId: number, id: number, userId: number): Promise<boolean> {
    const appreciation = await this.repository.findOne({
      where: { id, tenantId, userId },
    });
    return !!appreciation;
  }
}
