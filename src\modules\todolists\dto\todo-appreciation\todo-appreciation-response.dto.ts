import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của todo appreciation
 */
export class TodoAppreciationResponseDto {
  /**
   * ID của appreciation
   * @example 1
   */
  @ApiProperty({
    description: 'ID của appreciation',
    example: 1,
  })
  id: number;

  /**
   * ID của todo được appreciation
   * @example 1
   */
  @ApiProperty({
    description: 'ID của todo được appreciation',
    example: 1,
    nullable: true,
  })
  todoId: number | null;

  /**
   * ID của user tạo appreciation
   * @example 1
   */
  @ApiProperty({
    description: 'ID của user tạo appreciation',
    example: 1,
    nullable: true,
  })
  userId: number | null;

  /**
   * Ghi chú appreciation
   * @example 'Hoàn thành xuất sắc, đúng deadline và chất lượng cao'
   */
  @ApiProperty({
    description: 'Ghi chú appreciation',
    example: 'Hoàn thành xuất sắc, đúng deadline và chất lượng cao',
    nullable: true,
  })
  note: string | null;

  /**
   * Thờ<PERSON> gian t<PERSON> (timestamp)
   * @example 1640995200000
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1640995200000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * ID tenant
   * @example 1
   */
  @ApiProperty({
    description: 'ID tenant',
    example: 1,
    nullable: true,
  })
  tenantId: number | null;
}
