import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt, IsArray, ArrayMinSize, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tạo liên kết giữa task và key result
 */
export class CreateTaskKrDto {
  /**
   * ID của todo
   * @example 1
   */
  @ApiProperty({
    description: 'ID của todo',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID todo không được để trống' })
  @IsInt({ message: 'ID todo phải là số nguyên' })
  @Min(1, { message: 'ID todo phải lớn hơn 0' })
  taskId: number;

  /**
   * Danh sách ID của key results
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của key results',
    example: [1, 2, 3],
    required: true,
    type: [Number],
  })
  @IsNotEmpty({ message: 'Danh sách key result không được để trống' })
  @IsArray({ message: 'Key result IDs phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 key result' })
  @Type(() => Number)
  @IsInt({ each: true, message: 'Mỗi key result ID phải là số nguyên' })
  @Min(1, { each: true, message: 'Mỗi key result ID phải lớn hơn 0' })
  keyResultIds: number[];
}
