import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response analytics của dự án
 */
export class ProjectAnalyticsResponseDto {
  /**
   * ID dự án
   */
  @ApiProperty({
    description: 'ID dự án',
    example: 1,
  })
  projectId: number;

  /**
   * Tên dự án
   */
  @ApiProperty({
    description: 'Tên dự án',
    example: 'Dự án phát triển website',
  })
  projectName: string;

  /**
   * Tổng số công việc
   */
  @ApiProperty({
    description: 'Tổng số công việc trong dự án',
    example: 25,
  })
  totalTodos: number;

  /**
   * Số công việc đã hoàn thành
   */
  @ApiProperty({
    description: 'Số công việc đã hoàn thành',
    example: 15,
  })
  completedTodos: number;

  /**
   * Số công việc đang thực hiện
   */
  @ApiProperty({
    description: 'Số công việc đang thực hiện',
    example: 8,
  })
  inProgressTodos: number;

  /**
   * Số công việc chưa bắt đầu
   */
  @ApiProperty({
    description: 'Số công việc chưa bắt đầu',
    example: 2,
  })
  pendingTodos: number;

  /**
   * Số công việc quá hạn
   */
  @ApiProperty({
    description: 'Số công việc quá hạn',
    example: 3,
  })
  overdueTodos: number;

  /**
   * Tỷ lệ hoàn thành (%)
   */
  @ApiProperty({
    description: 'Tỷ lệ hoàn thành (%)',
    example: 60.0,
  })
  completionRate: number;

  /**
   * Điểm trung bình của dự án
   */
  @ApiProperty({
    description: 'Điểm trung bình của dự án',
    example: 4.2,
    nullable: true,
  })
  averageScore: number | null;

  /**
   * Thống kê theo trạng thái
   */
  @ApiProperty({
    description: 'Thống kê chi tiết theo trạng thái',
    example: {
      pending: 2,
      in_progress: 8,
      completed: 15,
      approved: 0,
      rejected: 0,
    },
  })
  statusBreakdown: Record<string, number>;

  /**
   * Thống kê theo mức độ ưu tiên
   */
  @ApiProperty({
    description: 'Thống kê theo mức độ ưu tiên',
    example: {
      low: 5,
      medium: 12,
      high: 6,
      urgent: 2,
    },
  })
  priorityBreakdown: Record<string, number>;

  /**
   * Thống kê theo thành viên
   */
  @ApiProperty({
    description: 'Thống kê theo thành viên',
    example: [
      {
        userId: 1,
        userName: 'Nguyễn Văn A',
        totalTodos: 10,
        completedTodos: 6,
        completionRate: 60.0,
      },
      {
        userId: 2,
        userName: 'Trần Thị B',
        totalTodos: 8,
        completedTodos: 5,
        completionRate: 62.5,
      },
    ],
  })
  memberStats: Array<{
    userId: number;
    userName: string;
    totalTodos: number;
    completedTodos: number;
    completionRate: number;
  }>;

  /**
   * Thống kê theo thời gian (7 ngày gần nhất)
   */
  @ApiProperty({
    description: 'Thống kê theo thời gian (7 ngày gần nhất)',
    example: [
      { date: '2024-01-01', completed: 2, created: 3 },
      { date: '2024-01-02', completed: 1, created: 2 },
      { date: '2024-01-03', completed: 3, created: 1 },
    ],
  })
  timelineStats: Array<{
    date: string;
    completed: number;
    created: number;
  }>;

  /**
   * Ngày bắt đầu dự án
   */
  @ApiProperty({
    description: 'Ngày bắt đầu dự án (timestamp)',
    example: 1640995200000,
    nullable: true,
  })
  startDate: number | null;

  /**
   * Ngày kết thúc dự án
   */
  @ApiProperty({
    description: 'Ngày kết thúc dự án (timestamp)',
    example: 1672531200000,
    nullable: true,
  })
  endDate: number | null;

  /**
   * Trạng thái dự án
   */
  @ApiProperty({
    description: 'Trạng thái dự án',
    example: 'active',
    nullable: true,
  })
  status: string | null;
}
