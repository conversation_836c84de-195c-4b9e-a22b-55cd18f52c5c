import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsInt, IsOptional, MaxLength } from 'class-validator';

/**
 * DTO cho tạo appreciation cho todo
 */
export class CreateTodoAppreciationDto {
  /**
   * ID của todo được appreciation
   * @example 1
   */
  @ApiProperty({
    description: 'ID của todo được appreciation',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID todo không được để trống' })
  @IsInt({ message: 'ID todo phải là số nguyên' })
  todoId: number;

  /**
   * Ghi chú appreciation
   * @example 'Hoàn thành xuất sắc, đúng deadline và chất lượng cao'
   */
  @ApiProperty({
    description: 'Ghi chú appreciation',
    example: 'Hoàn thành xuất sắc, đúng deadline và chất lượng cao',
    required: true,
  })
  @IsNotEmpty({ message: '<PERSON><PERSON> chú không được để trống' })
  @IsString({ message: '<PERSON><PERSON> chú phải là chuỗi' })
  @MaxLength(1000, { message: '<PERSON><PERSON> chú không được vượt quá 1000 ký tự' })
  note: string;
}
