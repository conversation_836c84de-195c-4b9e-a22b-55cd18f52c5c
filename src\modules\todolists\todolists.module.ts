import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '@/modules/auth/auth.module';
import { HrmModule } from '@/modules/hrm/hrm.module';

// Entities
import { Project } from './entities/project.entity';
import { ProjectMember } from './entities/project-members.entity';
import { Todo } from './entities/todo.entity';
import { TodoComment } from './entities/todo-comment.entity';
import { TodoAttachment } from './entities/todo-attachment.entity';
import { TodoCollaborator } from './entities/todo-collaborator.entity';
import { TodoTag } from './entities/todo-tag.entity';
import { TodoAppreciation } from './entities/todo-appreciation.entity';
import { TodoScore } from './entities/todo-score.entity';
import { TaskKr } from './entities/task-kr.entity';
import { Tag } from './entities/tag.entity';
import { TodoRating } from './entities/todo-rating.entity';
import { Campaign } from './entities/campaign.entity';
import { CampaignPhase } from './entities/campaign-phase.entity';
import { CampaignMilestone } from './entities/campaign-milestone.entity';

// Repositories
import {
  ProjectRepository,
  ProjectMemberRepository,
  TodoRepository,
  TaskKrRepository,
  TodoTagRepository,
  TodoScoreRepository,
  TodoCollaboratorRepository,
  TodoAttachmentRepository,
  TodoCommentRepository,
  TagRepository,
  TodoRatingRepository,
  TodoAppreciationRepository,
} from './repositories';
import { CampaignRepository } from './repositories/campaign.repository';

// Services
import { ProjectService } from './services/project.service';
import { TodoService } from './services/todo.service';
import { TodoTagService } from './services/todo-tag.service';
import { StatisticsService } from './services/statistics.service';
import { TodoCollaboratorService } from './services/todo-collaborator.service';
import { TodoAttachmentService } from './services/todo-attachment.service';
import { TodoCommentService } from './services/todo-comment.service';
import { EventService } from './services/event.service';
import { TagService } from './services/tag.service';
import { GanttChartService } from './services/gantt-chart.service';
import { DashboardService } from './services/dashboard.service';
import { TodoRatingService } from './services/todo-rating.service';
import { TodoAppreciationService } from './services/todo-appreciation.service';
import { TaskKrService } from './services/task-kr.service';
import { CampaignService } from './services/campaign.service';
import { TimelineService } from './services/timeline.service';

// Tools
import { TodoManagementTool } from './tools/todo-management.tool';
import { ProjectManagementTool } from './tools/project-management.tool';
import { AnalyticsTool } from './tools/analytics.tool';
import { SearchQueryTool } from './tools/search-query.tool';

// Controllers
import { ProjectController } from './controllers/project.controller';
import { TodoController } from './controllers/todo.controller';
import { StatisticsController } from './controllers/statistics.controller';
import { TodoCollaboratorController } from './controllers/todo-collaborator.controller';
import { TodoAttachmentController } from './controllers/todo-attachment.controller';
import { TodoCommentController } from './controllers/todo-comment.controller';
import { TagController } from './controllers/tag.controller';
import { GanttChartController } from './controllers/gantt-chart.controller';
import { DashboardController } from './controllers/dashboard.controller';
import { TodoRatingController } from './controllers/todo-rating.controller';
import { TodoAppreciationController } from './controllers/todo-appreciation.controller';
import { TaskKrController } from './controllers/task-kr.controller';
import { CampaignController } from './controllers/campaign.controller';
import { TimelineController } from './controllers/timeline.controller';

/**
 * Module quản lý công việc và dự án
 */
@Global()
@Module({
  imports: [
    AuthModule,
    HrmModule,
    TypeOrmModule.forFeature([
      Project,
      ProjectMember,
      Todo,
      TodoComment,
      TodoAttachment,
      TodoCollaborator,
      TodoTag,
      TodoAppreciation,
      TodoScore,
      TaskKr,
      Tag,
      TodoRating,
      Campaign,
      CampaignPhase,
      CampaignMilestone,
    ]),
  ],
  controllers: [
    ProjectController,
    TodoController,
    StatisticsController,
    TodoCollaboratorController,
    TodoAttachmentController,
    TodoCommentController,
    TagController,
    GanttChartController,
    DashboardController,
    TodoRatingController,
    TodoAppreciationController,
    TaskKrController,
    CampaignController,
    TimelineController,
  ],
  providers: [
    // Repositories
    ProjectRepository,
    ProjectMemberRepository,
    TodoRepository,
    TaskKrRepository,
    TodoTagRepository,
    TodoScoreRepository,
    TodoCollaboratorRepository,
    TodoAttachmentRepository,
    TodoCommentRepository,
    TagRepository,
    TodoRatingRepository,
    TodoAppreciationRepository,
    CampaignRepository,

    // Services
    ProjectService,
    TodoService,
    TodoTagService,
    StatisticsService,
    TodoCollaboratorService,
    TodoAttachmentService,
    TodoCommentService,
    EventService,
    TagService,
    GanttChartService,
    DashboardService,
    TodoRatingService,
    TodoAppreciationService,
    TaskKrService,
    CampaignService,
    TimelineService,

    // Tools
    TodoManagementTool,
    ProjectManagementTool,
    AnalyticsTool,
    SearchQueryTool,
  ],
  exports: [
    // Repositories
    ProjectRepository,
    ProjectMemberRepository,
    TodoRepository,
    TaskKrRepository,
    TodoTagRepository,
    TodoScoreRepository,
    TodoCollaboratorRepository,
    TodoAttachmentRepository,
    TodoCommentRepository,
    TagRepository,
    TodoRatingRepository,
    TodoAppreciationRepository,

    // Services
    ProjectService,
    TodoService,
    TodoTagService,
    StatisticsService,
    TodoCollaboratorService,
    TodoAttachmentService,
    TodoCommentService,
    EventService,
    TagService,
    GanttChartService,
    DashboardService,
    TodoRatingService,
    TodoAppreciationService,
    TaskKrService,
  ],
})
export class TodolistsModule {}
