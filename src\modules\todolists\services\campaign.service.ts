import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { CampaignRepository } from '../repositories/campaign.repository';
import { CreateCampaignDto } from '../dto/campaign/create-campaign.dto';
import { UpdateCampaignDto } from '../dto/campaign/update-campaign.dto';
import { CampaignResponseDto } from '../dto/campaign/campaign-response.dto';
import { CampaignQueryDto } from '../dto/campaign/campaign-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Service xử lý logic nghiệp vụ cho campaign
 */
@Injectable()
export class CampaignService {
  constructor(
    private readonly campaignRepository: CampaignRepository,
  ) {}

  /**
   * Tạo campaign mới
   * @param tenantId ID tenant
   * @param userId ID user tạo campaign
   * @param createDto Dữ liệu tạo campaign
   * @returns Campaign đã tạo
   */
  async createCampaign(
    tenantId: number,
    userId: number,
    createDto: CreateCampaignDto,
  ): Promise<CampaignResponseDto> {
    // Xác định người sở hữu campaign (mặc định là người tạo nếu không chỉ định)
    const ownerId = createDto.ownerId || userId;

    // Chuyển đổi dates sang timestamps
    const startDate = createDto.startDate ? new Date(createDto.startDate).getTime() : null;
    const endDate = createDto.endDate ? new Date(createDto.endDate).getTime() : null;

    const campaign = await this.campaignRepository.create(tenantId, {
      title: createDto.title,
      description: createDto.description,
      createdBy: userId,
      ownerId,
      startDate,
      endDate,
      priority: createDto.priority || 'medium',
      budget: createDto.budget,
      currency: createDto.currency || 'VND',
      objectives: createDto.objectives,
      targetAudience: createDto.targetAudience,
      channels: createDto.channels,
      tags: createDto.tags,
      status: 'draft',
      isActive: true,
    });

    return this.mapToResponseDto(campaign);
  }

  /**
   * Lấy danh sách campaign với phân trang
   * @param tenantId ID tenant
   * @param query Tham số truy vấn
   * @returns Danh sách campaign đã phân trang
   */
  async findAllCampaigns(
    tenantId: number,
    query: CampaignQueryDto,
  ): Promise<PaginatedResult<CampaignResponseDto>> {
    const paginatedResult = await this.campaignRepository.findAll(tenantId, query);

    return {
      items: paginatedResult.items.map(item => this.mapToResponseDto(item)),
      meta: paginatedResult.meta,
    };
  }

  /**
   * Lấy campaign theo ID
   * @param tenantId ID tenant
   * @param id ID campaign
   * @returns Campaign
   */
  async findCampaignById(
    tenantId: number,
    id: number,
  ): Promise<CampaignResponseDto> {
    const campaign = await this.campaignRepository.findById(tenantId, id);
    if (!campaign) {
      throw new NotFoundException(`Không tìm thấy campaign với ID ${id}`);
    }

    return this.mapToResponseDto(campaign);
  }

  /**
   * Cập nhật campaign
   * @param tenantId ID tenant
   * @param id ID campaign
   * @param userId ID user cập nhật
   * @param updateDto Dữ liệu cập nhật
   * @returns Campaign đã cập nhật
   */
  async updateCampaign(
    tenantId: number,
    id: number,
    userId: number,
    updateDto: UpdateCampaignDto,
  ): Promise<CampaignResponseDto> {
    // Kiểm tra campaign có tồn tại không
    const campaign = await this.campaignRepository.findById(tenantId, id);
    if (!campaign) {
      throw new NotFoundException(`Không tìm thấy campaign với ID ${id}`);
    }

    // Kiểm tra quyền cập nhật (chỉ owner hoặc creator mới được cập nhật)
    if (campaign.ownerId !== userId && campaign.createdBy !== userId) {
      throw new ForbiddenException('Bạn không có quyền cập nhật campaign này');
    }

    // Chuyển đổi dates sang timestamps nếu có
    const updateData: any = { ...updateDto };
    if (updateDto.startDate) {
      updateData.startDate = new Date(updateDto.startDate).getTime();
    }
    if (updateDto.endDate) {
      updateData.endDate = new Date(updateDto.endDate).getTime();
    }

    // Cập nhật campaign
    const updatedCampaign = await this.campaignRepository.update(
      tenantId,
      id,
      updateData,
    );

    if (!updatedCampaign) {
      throw new NotFoundException(`Không tìm thấy campaign với ID ${id} sau khi cập nhật`);
    }

    return this.mapToResponseDto(updatedCampaign);
  }

  /**
   * Xóa campaign
   * @param tenantId ID tenant
   * @param id ID campaign
   * @param userId ID user xóa
   * @returns Kết quả xóa
   */
  async deleteCampaign(
    tenantId: number,
    id: number,
    userId: number,
  ): Promise<boolean> {
    // Kiểm tra campaign có tồn tại không
    const campaign = await this.campaignRepository.findById(tenantId, id);
    if (!campaign) {
      throw new NotFoundException(`Không tìm thấy campaign với ID ${id}`);
    }

    // Kiểm tra quyền xóa (chỉ owner hoặc creator mới được xóa)
    if (campaign.ownerId !== userId && campaign.createdBy !== userId) {
      throw new ForbiddenException('Bạn không có quyền xóa campaign này');
    }

    return this.campaignRepository.delete(tenantId, id);
  }

  /**
   * Thay đổi trạng thái campaign
   * @param tenantId ID tenant
   * @param id ID campaign
   * @param userId ID user thay đổi trạng thái
   * @param status Trạng thái mới
   * @returns Campaign đã cập nhật
   */
  async updateCampaignStatus(
    tenantId: number,
    id: number,
    userId: number,
    status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled',
  ): Promise<CampaignResponseDto> {
    return this.updateCampaign(tenantId, id, userId, { status });
  }

  /**
   * Lấy thống kê campaign
   * @param tenantId ID tenant
   * @param id ID campaign
   * @param userId ID user xem thống kê
   * @returns Thống kê campaign
   */
  async getCampaignStats(
    tenantId: number,
    id: number,
    userId: number,
  ): Promise<{
    campaign: CampaignResponseDto;
    stats: {
      totalPhases: number;
      completedPhases: number;
      totalMilestones: number;
      completedMilestones: number;
      overdueMilestones: number;
    };
  }> {
    // Kiểm tra campaign có tồn tại không
    const campaign = await this.campaignRepository.findById(tenantId, id);
    if (!campaign) {
      throw new NotFoundException(`Không tìm thấy campaign với ID ${id}`);
    }

    // Kiểm tra quyền truy cập
    if (!await this.campaignRepository.canUserAccess(tenantId, id, userId)) {
      throw new ForbiddenException('Bạn không có quyền xem thống kê campaign này');
    }

    const stats = await this.campaignRepository.getCampaignStats(tenantId, id);

    return {
      campaign: this.mapToResponseDto(campaign),
      stats,
    };
  }

  /**
   * Chuyển đổi entity sang response DTO
   * @param campaign Entity campaign
   * @returns Response DTO
   */
  private mapToResponseDto(campaign: any): CampaignResponseDto {
    return {
      id: campaign.id,
      title: campaign.title,
      description: campaign.description,
      createdBy: campaign.createdBy,
      ownerId: campaign.ownerId,
      startDate: campaign.startDate,
      endDate: campaign.endDate,
      status: campaign.status,
      priority: campaign.priority,
      budget: campaign.budget,
      currency: campaign.currency,
      objectives: campaign.objectives,
      targetAudience: campaign.targetAudience,
      channels: campaign.channels,
      tags: campaign.tags,
      metadata: campaign.metadata,
      isActive: campaign.isActive,
      createdAt: campaign.createdAt,
      updatedAt: campaign.updatedAt,
      tenantId: campaign.tenantId,
    };
  }
}
