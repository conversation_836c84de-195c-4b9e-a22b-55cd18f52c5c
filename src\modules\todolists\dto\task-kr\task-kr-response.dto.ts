import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của task-kr mapping
 */
export class TaskKrResponseDto {
  /**
   * ID của task
   * @example 1
   */
  @ApiProperty({
    description: 'ID của task',
    example: 1,
  })
  taskId: number;

  /**
   * ID của key result
   * @example 1
   */
  @ApiProperty({
    description: 'ID của key result',
    example: 1,
  })
  krId: number;

  /**
   * ID tenant
   * @example 1
   */
  @ApiProperty({
    description: 'ID tenant',
    example: 1,
    nullable: true,
  })
  tenantId: number | null;

  /**
   * Thông tin key result (nếu có)
   */
  @ApiProperty({
    description: 'Thông tin key result',
    required: false,
    example: {
      id: 1,
      title: 'Tăng doanh thu 20%',
      description: 'Tăng doanh thu từ 100M lên 120M trong quý này',
      targetValue: 120000000,
      currentValue: 80000000,
      unit: 'VND',
    },
  })
  keyResult?: {
    id: number;
    title: string;
    description: string | null;
    targetValue: number;
    currentValue: number;
    unit: string | null;
  };

  /**
   * Thông tin todo (nếu có)
   */
  @ApiProperty({
    description: 'Thông tin todo',
    required: false,
    example: {
      id: 1,
      title: 'Hoàn thành báo cáo doanh thu',
      description: 'Tạo báo cáo chi tiết về doanh thu quý 4',
      status: 'in_progress',
    },
  })
  todo?: {
    id: number;
    title: string;
    description: string | null;
    status: string | null;
  };
}
