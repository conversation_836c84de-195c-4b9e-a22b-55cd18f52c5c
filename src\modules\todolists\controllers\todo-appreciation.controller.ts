import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/constants/swagger.constant';
import { TodoAppreciationService } from '../services/todo-appreciation.service';
import {
  CreateTodoAppreciationDto,
  UpdateTodoAppreciationDto,
  TodoAppreciationResponseDto,
  TodoAppreciationQueryDto,
} from '../dto/todo-appreciation';

/**
 * Controller xử lý các API liên quan đến todo appreciation
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(ApiResponseDto, TodoAppreciationResponseDto)
@Controller('/todos/appreciations')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class TodoAppreciationController {
  constructor(private readonly todoAppreciationService: TodoAppreciationService) {}

  /**
   * Tạo appreciation mới cho todo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo appreciation mới cho todo' })
  @ApiResponse({
    status: 201,
    description: 'Appreciation đã được tạo thành công',
    schema: ApiResponseDto.getSchema(TodoAppreciationResponseDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ hoặc todo chưa hoàn thành',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy todo',
  })
  async createAppreciation(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateTodoAppreciationDto,
  ): Promise<ApiResponseDto<TodoAppreciationResponseDto>> {
    const appreciation = await this.todoAppreciationService.createAppreciation(
      Number(user.tenantId),
      user.id,
      createDto,
    );
    return ApiResponseDto.created(appreciation, 'Tạo appreciation thành công');
  }

  /**
   * Lấy danh sách appreciation với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách appreciation với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách appreciation',
    schema: ApiResponseDto.getPaginatedSchema(TodoAppreciationResponseDto),
  })
  async findAllAppreciations(
    @CurrentUser() user: JwtPayload,
    @Query() query: TodoAppreciationQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TodoAppreciationResponseDto>>> {
    const paginatedAppreciations = await this.todoAppreciationService.findAllAppreciations(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(paginatedAppreciations, 'Lấy danh sách appreciation thành công');
  }

  /**
   * Lấy appreciation theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy appreciation theo ID' })
  @ApiParam({ name: 'id', description: 'ID appreciation', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin appreciation',
    schema: ApiResponseDto.getSchema(TodoAppreciationResponseDto),
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy appreciation',
  })
  async findAppreciationById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TodoAppreciationResponseDto>> {
    const appreciation = await this.todoAppreciationService.findAppreciationById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(appreciation, 'Lấy thông tin appreciation thành công');
  }

  /**
   * Cập nhật appreciation
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật appreciation' })
  @ApiParam({ name: 'id', description: 'ID appreciation', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Appreciation đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(TodoAppreciationResponseDto),
  })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền cập nhật appreciation này',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy appreciation',
  })
  async updateAppreciation(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateTodoAppreciationDto,
  ): Promise<ApiResponseDto<TodoAppreciationResponseDto>> {
    const appreciation = await this.todoAppreciationService.updateAppreciation(
      Number(user.tenantId),
      id,
      user.id,
      updateDto,
    );
    return ApiResponseDto.success(appreciation, 'Cập nhật appreciation thành công');
  }

  /**
   * Xóa appreciation
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa appreciation' })
  @ApiParam({ name: 'id', description: 'ID appreciation', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Appreciation đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền xóa appreciation này',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy appreciation',
  })
  async deleteAppreciation(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    await this.todoAppreciationService.deleteAppreciation(
      Number(user.tenantId),
      id,
      user.id,
    );
    return ApiResponseDto.deleted('Xóa appreciation thành công');
  }
}
