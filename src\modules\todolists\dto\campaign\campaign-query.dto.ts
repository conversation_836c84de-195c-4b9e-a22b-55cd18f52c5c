import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsArray, IsNumber, IsDateString, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query campaign
 */
export class CampaignQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: ['draft', 'active', 'paused', 'completed', 'cancelled'],
    required: false,
    example: 'active',
  })
  @IsOptional()
  @IsEnum(['draft', 'active', 'paused', 'completed', 'cancelled'], { message: 'Trạng thái không hợp lệ' })
  status?: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';

  /**
   * Lọc theo mức độ ưu tiên
   */
  @ApiProperty({
    description: 'Lọ<PERSON> theo mức độ ưu tiên',
    enum: ['low', 'medium', 'high', 'urgent'],
    required: false,
    example: 'high',
  })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high', 'urgent'], { message: 'Mức độ ưu tiên không hợp lệ' })
  priority?: 'low' | 'medium' | 'high' | 'urgent';

  /**
   * Lọc theo ID người sở hữu
   */
  @ApiProperty({
    description: 'Lọc theo ID người sở hữu',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'ID người sở hữu phải là số' })
  ownerId?: number;

  /**
   * Lọc theo ID người tạo
   */
  @ApiProperty({
    description: 'Lọc theo ID người tạo',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'ID người tạo phải là số' })
  createdBy?: number;

  /**
   * Tìm kiếm theo từ khóa trong tên và mô tả
   */
  @ApiProperty({
    description: 'Tìm kiếm theo từ khóa trong tên và mô tả',
    example: 'marketing',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa phải là chuỗi' })
  keyword?: string;

  /**
   * Lọc theo tags
   */
  @ApiProperty({
    description: 'Lọc theo tags',
    example: ['marketing', 'q4-2024'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[];

  /**
   * Lọc theo kênh truyền thông
   */
  @ApiProperty({
    description: 'Lọc theo kênh truyền thông',
    example: ['facebook', 'google-ads'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Kênh truyền thông phải là mảng' })
  @IsString({ each: true, message: 'Mỗi kênh phải là chuỗi' })
  channels?: string[];

  /**
   * Ngày bắt đầu từ (ISO string)
   */
  @ApiProperty({
    description: 'Ngày bắt đầu từ (ISO string)',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDateFrom?: string;

  /**
   * Ngày bắt đầu đến (ISO string)
   */
  @ApiProperty({
    description: 'Ngày bắt đầu đến (ISO string)',
    example: '2024-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDateTo?: string;

  /**
   * Ngày kết thúc từ (ISO string)
   */
  @ApiProperty({
    description: 'Ngày kết thúc từ (ISO string)',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDateFrom?: string;

  /**
   * Ngày kết thúc đến (ISO string)
   */
  @ApiProperty({
    description: 'Ngày kết thúc đến (ISO string)',
    example: '2024-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDateTo?: string;

  /**
   * Ngân sách tối thiểu
   */
  @ApiProperty({
    description: 'Ngân sách tối thiểu',
    example: 1000000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Ngân sách tối thiểu phải là số' })
  minBudget?: number;

  /**
   * Ngân sách tối đa
   */
  @ApiProperty({
    description: 'Ngân sách tối đa',
    example: 10000000000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Ngân sách tối đa phải là số' })
  maxBudget?: number;
}
