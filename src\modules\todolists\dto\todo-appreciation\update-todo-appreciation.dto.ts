import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, MaxLength } from 'class-validator';

/**
 * DTO cho cập nhật appreciation cho todo
 */
export class UpdateTodoAppreciationDto {
  /**
   * Ghi chú appreciation
   * @example 'Hoàn thành xuất sắc, đúng deadline và chất lượng cao'
   */
  @ApiProperty({
    description: 'Ghi chú appreciation',
    example: 'Hoàn thành xuất sắc, đúng deadline và chất lượng cao',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON>hi chú phải là chuỗi' })
  @MaxLength(1000, { message: '<PERSON>hi chú không được vượt quá 1000 ký tự' })
  note?: string;
}
