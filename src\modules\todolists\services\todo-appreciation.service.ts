import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { TodoAppreciationRepository } from '../repositories/todo-appreciation.repository';
import { TodoRepository } from '../repositories/todo.repository';
import { CreateTodoAppreciationDto } from '../dto/todo-appreciation/create-todo-appreciation.dto';
import { UpdateTodoAppreciationDto } from '../dto/todo-appreciation/update-todo-appreciation.dto';
import { TodoAppreciationResponseDto } from '../dto/todo-appreciation/todo-appreciation-response.dto';
import { TodoAppreciationQueryDto } from '../dto/todo-appreciation/todo-appreciation-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { TodoStatus } from '../enum/todo-status.enum';

/**
 * Service xử lý logic nghiệp vụ cho todo appreciation
 */
@Injectable()
export class TodoAppreciationService {
  constructor(
    private readonly todoAppreciationRepository: TodoAppreciationRepository,
    private readonly todoRepository: TodoRepository,
  ) {}

  /**
   * Tạo appreciation mới cho todo
   * @param tenantId ID tenant
   * @param userId ID user tạo appreciation
   * @param createDto Dữ liệu tạo appreciation
   * @returns Appreciation đã tạo
   */
  async createAppreciation(
    tenantId: number,
    userId: number,
    createDto: CreateTodoAppreciationDto,
  ): Promise<TodoAppreciationResponseDto> {
    // Kiểm tra todo có tồn tại không
    const todo = await this.todoRepository.findById(tenantId, createDto.todoId);
    if (!todo) {
      throw new NotFoundException(`Không tìm thấy todo với ID ${createDto.todoId}`);
    }

    // Kiểm tra todo đã hoàn thành chưa
    if (todo.status !== TodoStatus.COMPLETED && todo.status !== TodoStatus.APPROVED) {
      throw new BadRequestException('Chỉ có thể tạo appreciation cho todo đã hoàn thành');
    }

    // Tạo appreciation
    const appreciation = await this.todoAppreciationRepository.create(tenantId, {
      todoId: createDto.todoId,
      userId,
      note: createDto.note,
    });

    return this.mapToResponseDto(appreciation);
  }

  /**
   * Lấy danh sách appreciation với phân trang
   * @param tenantId ID tenant
   * @param query Tham số truy vấn
   * @returns Danh sách appreciation đã phân trang
   */
  async findAllAppreciations(
    tenantId: number,
    query: TodoAppreciationQueryDto,
  ): Promise<PaginatedResult<TodoAppreciationResponseDto>> {
    const paginatedResult = await this.todoAppreciationRepository.findAll(tenantId, query);

    return {
      items: paginatedResult.items.map(item => this.mapToResponseDto(item)),
      meta: paginatedResult.meta,
    };
  }

  /**
   * Lấy appreciation theo ID
   * @param tenantId ID tenant
   * @param id ID appreciation
   * @returns Appreciation
   */
  async findAppreciationById(
    tenantId: number,
    id: number,
  ): Promise<TodoAppreciationResponseDto> {
    const appreciation = await this.todoAppreciationRepository.findById(tenantId, id);
    if (!appreciation) {
      throw new NotFoundException(`Không tìm thấy appreciation với ID ${id}`);
    }

    return this.mapToResponseDto(appreciation);
  }

  /**
   * Lấy danh sách appreciation theo todoId
   * @param tenantId ID tenant
   * @param todoId ID todo
   * @returns Danh sách appreciation
   */
  async findAppreciationsByTodoId(
    tenantId: number,
    todoId: number,
  ): Promise<TodoAppreciationResponseDto[]> {
    // Kiểm tra todo có tồn tại không
    const todo = await this.todoRepository.findById(tenantId, todoId);
    if (!todo) {
      throw new NotFoundException(`Không tìm thấy todo với ID ${todoId}`);
    }

    const appreciations = await this.todoAppreciationRepository.findByTodoId(tenantId, todoId);
    return appreciations.map(item => this.mapToResponseDto(item));
  }

  /**
   * Cập nhật appreciation
   * @param tenantId ID tenant
   * @param id ID appreciation
   * @param userId ID user cập nhật
   * @param updateDto Dữ liệu cập nhật
   * @returns Appreciation đã cập nhật
   */
  async updateAppreciation(
    tenantId: number,
    id: number,
    userId: number,
    updateDto: UpdateTodoAppreciationDto,
  ): Promise<TodoAppreciationResponseDto> {
    // Kiểm tra appreciation có tồn tại không
    const appreciation = await this.todoAppreciationRepository.findById(tenantId, id);
    if (!appreciation) {
      throw new NotFoundException(`Không tìm thấy appreciation với ID ${id}`);
    }

    // Kiểm tra quyền cập nhật (chỉ người tạo mới được cập nhật)
    if (appreciation.userId !== userId) {
      throw new ForbiddenException('Bạn không có quyền cập nhật appreciation này');
    }

    // Cập nhật appreciation
    const updatedAppreciation = await this.todoAppreciationRepository.update(
      tenantId,
      id,
      updateDto,
    );

    if (!updatedAppreciation) {
      throw new NotFoundException(`Không tìm thấy appreciation với ID ${id} sau khi cập nhật`);
    }

    return this.mapToResponseDto(updatedAppreciation);
  }

  /**
   * Xóa appreciation
   * @param tenantId ID tenant
   * @param id ID appreciation
   * @param userId ID user xóa
   * @returns Kết quả xóa
   */
  async deleteAppreciation(
    tenantId: number,
    id: number,
    userId: number,
  ): Promise<boolean> {
    // Kiểm tra appreciation có tồn tại không
    const appreciation = await this.todoAppreciationRepository.findById(tenantId, id);
    if (!appreciation) {
      throw new NotFoundException(`Không tìm thấy appreciation với ID ${id}`);
    }

    // Kiểm tra quyền xóa (chỉ người tạo mới được xóa)
    if (appreciation.userId !== userId) {
      throw new ForbiddenException('Bạn không có quyền xóa appreciation này');
    }

    return this.todoAppreciationRepository.delete(tenantId, id);
  }

  /**
   * Chuyển đổi entity sang response DTO
   * @param appreciation Entity appreciation
   * @returns Response DTO
   */
  private mapToResponseDto(appreciation: any): TodoAppreciationResponseDto {
    return {
      id: appreciation.id,
      todoId: appreciation.todoId,
      userId: appreciation.userId,
      note: appreciation.note,
      createdAt: appreciation.createdAt,
      tenantId: appreciation.tenantId,
    };
  }
}
