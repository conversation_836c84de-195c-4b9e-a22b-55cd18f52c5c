import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả cập nhật nhiều công việc
 */
export class BulkUpdateTodoResponseDto {
  /**
   * Tổng số công việc được yêu cầu cập nhật
   */
  @ApiProperty({
    description: 'Tổng số công việc được yêu cầu cập nhật',
    example: 5,
    type: Number,
  })
  totalRequested: number;

  /**
   * Số công việc đã cập nhật thành công
   */
  @ApiProperty({
    description: 'Số công việc đã cập nhật thành công',
    example: 3,
    type: Number,
  })
  successCount: number;

  /**
   * Số công việc không thể cập nhật
   */
  @ApiProperty({
    description: 'Số công việc không thể cập nhật',
    example: 2,
    type: Number,
  })
  failureCount: number;

  /**
   * <PERSON><PERSON> sách ID các công việc đã cập nhật thành công
   */
  @ApiProperty({
    description: '<PERSON>h sách ID các công việc đã cập nhật thành công',
    example: [1, 3, 5],
    type: [Number],
  })
  updatedIds: number[];

  /**
   * Danh sách các công việc không thể cập nhật với lý do
   */
  @ApiProperty({
    description: 'Danh sách các công việc không thể cập nhật với lý do',
    example: [
      { id: 2, reason: 'Không tìm thấy công việc' },
      { id: 4, reason: 'Bạn không có quyền cập nhật công việc này' }
    ],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'number' },
        reason: { type: 'string' }
      }
    }
  })
  failures: Array<{ id: number; reason: string }>;
}
