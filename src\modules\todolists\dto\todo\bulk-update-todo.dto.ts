import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize, IsOptional, IsEnum, IsInt, <PERSON>, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { TodoStatus } from '../../enum/todo-status.enum';
import { TodoPriority } from '../../enum/todo-priority.enum';

/**
 * DTO cho việc cập nhật nhiều công việc
 */
export class BulkUpdateTodoDto {
  /**
   * Danh sách ID các công việc cần cập nhật
   */
  @ApiProperty({
    description: 'Danh sách ID các công việc cần cập nhật',
    example: [1, 2, 3],
    type: [Number],
    required: true,
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1, { message: '<PERSON><PERSON><PERSON> có ít nhất 1 công việc để cập nhật' })
  @IsNumber({}, { each: true, message: 'Mỗi ID phải là số' })
  @Type(() => Number)
  ids: number[];

  /**
   * Trạng thái mới (nếu cập nhật)
   */
  @ApiProperty({
    description: 'Trạng thái mới cho tất cả công việc',
    enum: TodoStatus,
    required: false,
    example: TodoStatus.IN_PROGRESS,
  })
  @IsOptional()
  @IsEnum(TodoStatus, { message: 'Trạng thái không hợp lệ' })
  status?: TodoStatus;

  /**
   * Mức độ ưu tiên mới (nếu cập nhật)
   */
  @ApiProperty({
    description: 'Mức độ ưu tiên mới cho tất cả công việc',
    enum: TodoPriority,
    required: false,
    example: TodoPriority.HIGH,
  })
  @IsOptional()
  @IsEnum(TodoPriority, { message: 'Mức độ ưu tiên không hợp lệ' })
  priority?: TodoPriority;

  /**
   * ID người được giao mới (nếu cập nhật)
   */
  @ApiProperty({
    description: 'ID người được giao mới cho tất cả công việc',
    required: false,
    example: 5,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID người được giao phải là số nguyên' })
  @Min(1, { message: 'ID người được giao phải lớn hơn 0' })
  assigneeId?: number;

  /**
   * ID dự án mới (nếu cập nhật)
   */
  @ApiProperty({
    description: 'ID dự án mới cho tất cả công việc',
    required: false,
    example: 2,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID dự án phải là số nguyên' })
  @Min(1, { message: 'ID dự án phải lớn hơn 0' })
  categoryId?: number;

  /**
   * Deadline mới (timestamp)
   */
  @ApiProperty({
    description: 'Deadline mới cho tất cả công việc (timestamp)',
    required: false,
    example: 1640995200000,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Deadline phải là số nguyên' })
  @Min(0, { message: 'Deadline phải là số dương' })
  deadline?: number;

  /**
   * Số sao kỳ vọng mới (1-5)
   */
  @ApiProperty({
    description: 'Số sao kỳ vọng mới cho tất cả công việc (1-5)',
    required: false,
    example: 4,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Số sao kỳ vọng phải là số nguyên' })
  @Min(1, { message: 'Số sao kỳ vọng phải từ 1 đến 5' })
  @Max(5, { message: 'Số sao kỳ vọng phải từ 1 đến 5' })
  expectedStars?: number;
}
