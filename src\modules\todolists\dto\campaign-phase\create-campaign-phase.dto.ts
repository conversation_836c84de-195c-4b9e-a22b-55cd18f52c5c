import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsEnum, IsArray, IsNumber, IsDateString, MaxLength, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tạo giai đoạn chiến dịch mới
 */
export class CreateCampaignPhaseDto {
  /**
   * ID chiến dịch
   */
  @ApiProperty({
    description: 'ID chiến dịch',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID chiến dịch không được để trống' })
  @Type(() => Number)
  @IsNumber({}, { message: 'ID chiến dịch phải là số' })
  @Min(1, { message: 'ID chiến dịch phải lớn hơn 0' })
  campaignId: number;

  /**
   * Tên giai đoạn
   */
  @ApiProperty({
    description: 'Tên giai đoạn',
    example: 'Giai đoạn 1: <PERSON><PERSON><PERSON><PERSON> cứu thị trường',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên giai đoạn không được để trống' })
  @IsString({ message: 'Tên giai đoạn phải là chuỗi' })
  @MaxLength(255, { message: 'Tên giai đoạn không được vượt quá 255 ký tự' })
  title: string;

  /**
   * Mô tả giai đoạn
   */
  @ApiProperty({
    description: 'Mô tả giai đoạn',
    example: 'Nghiên cứu và phân tích thị trường mục tiêu',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Thứ tự giai đoạn
   */
  @ApiProperty({
    description: 'Thứ tự giai đoạn',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'Thứ tự giai đoạn không được để trống' })
  @Type(() => Number)
  @IsNumber({}, { message: 'Thứ tự giai đoạn phải là số' })
  @Min(1, { message: 'Thứ tự giai đoạn phải lớn hơn 0' })
  order: number;

  /**
   * Ngày bắt đầu giai đoạn
   */
  @ApiProperty({
    description: 'Ngày bắt đầu giai đoạn (ISO string)',
    example: '2024-10-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  /**
   * Ngày kết thúc giai đoạn
   */
  @ApiProperty({
    description: 'Ngày kết thúc giai đoạn (ISO string)',
    example: '2024-10-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  /**
   * Mức độ ưu tiên
   */
  @ApiProperty({
    description: 'Mức độ ưu tiên',
    enum: ['low', 'medium', 'high', 'urgent'],
    example: 'medium',
    required: false,
    default: 'medium',
  })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high', 'urgent'], { message: 'Mức độ ưu tiên không hợp lệ' })
  priority?: 'low' | 'medium' | 'high' | 'urgent';

  /**
   * Ngân sách giai đoạn
   */
  @ApiProperty({
    description: 'Ngân sách giai đoạn',
    example: 200000000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Ngân sách phải là số' })
  @Min(0, { message: 'Ngân sách phải lớn hơn hoặc bằng 0' })
  budget?: number;

  /**
   * Mục tiêu giai đoạn
   */
  @ApiProperty({
    description: 'Mục tiêu giai đoạn',
    example: 'Hoàn thành nghiên cứu thị trường và xác định nhóm khách hàng mục tiêu',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mục tiêu phải là chuỗi' })
  objectives?: string;

  /**
   * Kết quả mong đợi
   */
  @ApiProperty({
    description: 'Kết quả mong đợi',
    example: 'Báo cáo nghiên cứu thị trường chi tiết và chiến lược tiếp cận khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Kết quả mong đợi phải là chuỗi' })
  expectedResults?: string;

  /**
   * Tiêu chí hoàn thành
   */
  @ApiProperty({
    description: 'Tiêu chí hoàn thành',
    example: 'Hoàn thành 100% khảo sát và phân tích dữ liệu',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tiêu chí hoàn thành phải là chuỗi' })
  completionCriteria?: string;

  /**
   * ID người phụ trách giai đoạn
   */
  @ApiProperty({
    description: 'ID người phụ trách giai đoạn',
    example: 2,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'ID người phụ trách phải là số' })
  @Min(1, { message: 'ID người phụ trách phải lớn hơn 0' })
  assigneeId?: number;

  /**
   * Phụ thuộc vào giai đoạn nào (IDs)
   */
  @ApiProperty({
    description: 'Phụ thuộc vào giai đoạn nào (IDs)',
    example: [1, 2],
    required: false,
    type: [Number],
  })
  @IsOptional()
  @IsArray({ message: 'Dependencies phải là mảng' })
  @Type(() => Number)
  @IsNumber({}, { each: true, message: 'Mỗi dependency ID phải là số' })
  @Min(1, { each: true, message: 'Mỗi dependency ID phải lớn hơn 0' })
  dependencies?: number[];
}
