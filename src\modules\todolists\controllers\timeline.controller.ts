import {
  Controller,
  Get,
  Post,
  Put,
  Param,
  Body,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/constants/swagger.constant';
import { TimelineService } from '../services/timeline.service';

/**
 * Controller xử lý các API liên quan đến timeline management
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(ApiResponseDto)
@Controller('/campaigns/:campaignId/timeline')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class TimelineController {
  constructor(private readonly timelineService: TimelineService) {}

  /**
   * Tạo timeline cho campaign
   */
  @Post()
  @ApiOperation({ summary: 'Tạo timeline cho campaign' })
  @ApiParam({ name: 'campaignId', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 201,
    description: 'Timeline đã được tạo thành công',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy campaign',
  })
  async createCampaignTimeline(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId', ParseIntPipe) campaignId: number,
  ): Promise<ApiResponseDto<any>> {
    const timeline = await this.timelineService.createCampaignTimeline(
      Number(user.tenantId),
      campaignId,
      user.id,
    );
    return ApiResponseDto.created(timeline, 'Tạo timeline thành công');
  }

  /**
   * Lấy timeline của campaign
   */
  @Get()
  @ApiOperation({ summary: 'Lấy timeline của campaign' })
  @ApiParam({ name: 'campaignId', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Timeline của campaign',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy campaign',
  })
  async getCampaignTimeline(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId', ParseIntPipe) campaignId: number,
  ): Promise<ApiResponseDto<any>> {
    const timeline = await this.timelineService.getCampaignTimeline(
      Number(user.tenantId),
      campaignId,
      user.id,
    );
    return ApiResponseDto.success(timeline, 'Lấy timeline thành công');
  }

  /**
   * Cập nhật timeline cho campaign
   */
  @Put()
  @ApiOperation({ summary: 'Cập nhật timeline cho campaign' })
  @ApiParam({ name: 'campaignId', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Timeline đã được cập nhật thành công',
  })
  async updateCampaignTimeline(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId', ParseIntPipe) campaignId: number,
    @Body() timelineData: any,
  ): Promise<ApiResponseDto<any>> {
    const timeline = await this.timelineService.updateCampaignTimeline(
      Number(user.tenantId),
      campaignId,
      user.id,
      timelineData,
    );
    return ApiResponseDto.success(timeline, 'Cập nhật timeline thành công');
  }

  /**
   * Kiểm tra dependencies của phases
   */
  @Get('validate-dependencies')
  @ApiOperation({ summary: 'Kiểm tra dependencies của phases' })
  @ApiParam({ name: 'campaignId', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra dependencies',
  })
  async validatePhaseDependencies(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId', ParseIntPipe) campaignId: number,
  ): Promise<ApiResponseDto<any>> {
    const validation = await this.timelineService.validatePhaseDependencies(
      Number(user.tenantId),
      campaignId,
    );
    return ApiResponseDto.success(validation, 'Kiểm tra dependencies thành công');
  }

  /**
   * Tự động sắp xếp phases theo dependencies
   */
  @Post('auto-arrange')
  @ApiOperation({ summary: 'Tự động sắp xếp phases theo dependencies' })
  @ApiParam({ name: 'campaignId', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Phases đã được sắp xếp tự động',
  })
  async autoArrangePhases(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId', ParseIntPipe) campaignId: number,
  ): Promise<ApiResponseDto<any>> {
    const arrangement = await this.timelineService.autoArrangePhases(
      Number(user.tenantId),
      campaignId,
      user.id,
    );
    return ApiResponseDto.success(arrangement, 'Sắp xếp phases tự động thành công');
  }

  /**
   * Tính toán critical path của campaign
   */
  @Get('critical-path')
  @ApiOperation({ summary: 'Tính toán critical path của campaign' })
  @ApiParam({ name: 'campaignId', description: 'ID campaign', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Critical path của campaign',
  })
  async calculateCriticalPath(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId', ParseIntPipe) campaignId: number,
  ): Promise<ApiResponseDto<any>> {
    const criticalPath = await this.timelineService.calculateCriticalPath(
      Number(user.tenantId),
      campaignId,
    );
    return ApiResponseDto.success(criticalPath, 'Tính toán critical path thành công');
  }
}
